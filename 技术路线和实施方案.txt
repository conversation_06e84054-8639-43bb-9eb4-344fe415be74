2.5 技术路线与实施方案
项目将按图 2 所示的“面向源代码与二进制代码的软件成分分析技术研究，异构复杂软件供应链风险评估体系研究，基于信创国产化场景的异构复杂软件供应链风险管控与治理平台，异构复杂软件供应链安全治理的典型应用场景示范验证”四大任务展开。

图 2 项目实施方案和技术路线
2.5.1面向源代码与二进制代码的软件成分分析技术研究
面向源代码与二进制代码的软件成分分析技术需求，本课题围绕异构复杂软件成分分析技术所需的自主可控、安全合规、生态兼容适配、高效可靠和供应链安全等要求，研究代码特征快速提取技术、源代码软件成分分析技术以及二进制代码软件成分分析技术。具体的研究技术路线和技术方案如下图所示。

图 3 研究技术路线和技术方案图
2.5.1.1 代码特征快速提取技术研究

(1)基于增量代码片段基因标记技术的源代码特征快速提取技术研究
在工业界中，代码克隆同源检测的研究已长达几十年之久，经过大量学者不断地研究，取得了不断地进展。一般的，业内将代码克隆定义为四种类型：
类型一（完全相同的代码）：除空格、注释之外，两个代码片段完全相同。
类型二（重命名/参数化代码）：除变量名、类型名、函数名之外完全相同。
类型三（几乎相同的代码）：存在语句增删、使用不同标识符、文字、类型、空格、布局、注释，但依然相似的代码对。
类型四（语义相似的代码）：相同功能的异构代码，在文本或者语法上不相似，但在语义上有相似性。
无论是对于上述四种类型的哪一种，都是按照一个方式将代码片段计算为一段哈希或一段向量。无论是哈希还是向量，在面对全球代码库中的海量代码片段时，能够进行代码片段快速提取以及快速比对，是解决代码克隆同源检测的关键一环。
对代码片段基因库的构建，关键问题在于如何降低在构建代码片段基因库过程中，对代码变更的检测和提取的复杂度。传统构建时都是通过每个开源项目的每一个Commit提交提取一次代码片段基因，对于开源项目发生增量代码的变化时，99%的情况变更的代码范围都不超过总代码数的10%，若对每一个Commit提交都提取一次代码片段基因，则大大增加了工作复杂度，已经难以胜任千万行级别甚至上亿行级别的代码检测任务。此外，代码增加与迭代速度也日益变快，比如开源仓库Github中每天都会有成千上万的贡献者贡献代码，在这样的情况下，如果每次都进行全量的代码克隆检测将花费较高的代价。
如下图所示，本项目针对上述问题，研究了一种用于增量源代码特征快速提取的技术。具体步骤如下：
代码基因标记：对增量代码及其前后相邻的代码片段进行基因标记。
数据记录：记录同一文件内所有基因序列及变更行数的变化量。
增量计算：仅计算并记录增量代码的基因特征及其具体内容，忽略非增量部分的数据存储。
基因对比：在进行代码基因对比时，如果增量哈希的行数变化占总行数的比例超过预设阈值，则直接认定其基因相同；对于不满足该条件的增量哈希，则进一步将增量代码还原并与源代码进行全量哈希对比。

图 4 增量代码片段基因标记技术的源代码特征快速提取
通过这种方法，可以有效地提高增量源代码特征提取的效率和准确性。从而解决开源项目源代码提取慢、数据冗余的问题，在高频率增量代码的场景下达到开销与性能的平衡。
(2)适用于二进制代码特征快速提取技术研究
信创环境下软件大多以二进制文件形式存在，且基于对软件产权的保护很多软件无法获得源码，当软件所使用的组件出现安全漏洞时，需要能够立刻确认二进制软件包中是否受该组件函数影响。传统检测方法一是通过组件版本和关联漏洞，二是通过提取对应出现安全漏洞函数在二进制代码中的字节签名，在目标二进制中搜索是否包含该函数字节签名。
存在以下情况时，无法检测出软件是否受到威胁：
1.软件开发过程中，仅使用部分函数代码，未使用这个组件。
2.软件编译过程中，编译器自动去除未使用过的函数，其中可能还包含组件版本关联的函数。
3.同一段函数代码，经过不同的编译器、编译参数、操作系统平台以及处理器架构的处理后，可能生成多种二进制变种，从而导致函数字节签名不一致。
在二进制领域，已经有许多方法被提出用于判断两个二进制函数的相似性，以下是几种具有代表性的技术：
1.基于哈希的相似函数比较技术：该方法通过将二进制文件中的函数数据按照一定规则分片，并计算每片数据的哈希值。比较时，通过统计相同哈希值的数量来衡量相似度，相同哈希值数量越多，相似度越高。虽然哈希技术能够以较低成本实现快速查找，但在异构环境下，任何细微的变化都会导致哈希值变化，从而降低准确性。
2.基于指令序列的相似函数比较技术：编译后的函数指令在数据空间中按顺序排列。该技术将指令中的操作码和操作数进行归一化处理，并根据操作码的类别进行标记，进而对比两个函数的相似性。然而，由于跨平台、跨架构等因素导致的差异，指令归一化可能会丢失精确的语义信息，从而影响语义层面的比较，无法涵盖异构场景。
3.基于控制流图的函数相似比较技术：BinDiff 是一个典型的基于控制流图的二进制相似性比较工具。它通过将两个二进制文件中函数的控制流图以图结构进行比较。然而，不同的编译优化等级会对控制流图产生显著影响，导致该方法在跨编译优化等级时表现较差。此外，它对函数语义的支持也较为有限。另一种工具 Diaphora 通过函数、伪代码、汇编代码和调用图等多种特征粒度，结合人工选定的特征来判断函数相似性。然而，由于异构系统导致二进制函数的最终表示存在差异，无法确保所有特征在跨平台和跨架构场景下保持一致，进而影响匹配效果。此外，Diaphora 对每个函数相似性的判断过于依赖人工介入。
4.DeepBindiff利用自然语言处理技术：该技术利用自然语言处理技术来分析汇编指令和控制流图结构，结合基本块和图结构，生成函数的嵌入向量（embedding）。然而，它对函数基本块和控制流结构过于敏感，尤其是在跨架构场景下，只关注低层次特征，导致嵌入的代码片段缺乏解释性，从而降低了对异构环境的支持能力。
针对以上情况，研究适用于二进制代码特征快速提取的技术，尤其是针对信创环境下异构复杂的二进制场景，能够通过提取出的二进制代码特征中的函数信息，识别出其中的安全漏洞函数信息。
(3)基于大语言模型开源组件漏洞缺陷提取技术研究
通过大语言模型和抽象语法树来提取组件漏洞的缺陷函数，主要分漏洞缺陷函数识别和关联函数提取两部分。

图 5 大语言模型漏洞缺陷函数识别方案
(a)大语言模型漏洞缺陷函数识别方案。通过监控GitHub、GitLab等平台的组件项目仓库，捕捉提交中的代码变更内容。确保系统能够获取最新的代码提交记录，尤其是涉及漏洞修复的代码变更。通过抓取提交日志和代码差异内容，建立代码变更数据源，为后续的分析奠定基础。获取到代码提交记录后，通过基于大语言模型的缺陷修复行为模型，对代码变更进行深入分析，利用模型对提交内容的语义进行理解，结合代码注释、提交信息等进行判断，识别出潜在的缺陷修复行为，区分普通代码变更和漏洞修复提交，获取缺陷代码所在函数信息。
(b) 漏洞缺陷关联函数识别方案。通过大语言模型识别出的缺陷函数，首先利用AST（抽象语法树）解析其代码结构，重点分析该函数的调用关系，如图：

图 6 漏洞缺陷关联函数识别方案
系统自动遍历所有调用该缺陷函数的其他函数，识别和提取这些关联函数，形成完整的调用链图。这种方式能够快速定位开源组件中可能受到影响的函数，输出组件漏洞风险关系函数列表，为后续的修复和安全评估提供依据。
2.5.1.2 源代码软件成分分析技术研究
(1)基于自监督学习的跨语言代码克隆检测方法研究
A.方法整体结构
该方法主要包括三个部分：
a) 数据预处理。去除与源码无关的信息，利用语法解析工具将代码解析为AST, 并对AST的节点进行上下文信息扩展。
b) 同义词转换。对不同编程语言的AST进行同义词转换处理。通过建立同义词转换表，统一不同语言中的节点类型和节点值，以减少不同编程语言AST之间的差异，确保不同语言的AST可以进行有效比较。
c) 代码克隆检测模型。将处理后的AST输入到基于对比学习的树卷积神经网络中。通过对比学习，模型最小化克隆代码对之间的距离，最大化非克隆代码对之间的距离，从而提高检测的准确性。最终，通过计算代码表示的相似度来判断是否存在跨语言代码克隆。

图 7 基于自监督学习的跨语言代码克隆检测方法整体框架
B.数据预处理
数据预处理是对源代码进行解析和增强处理，以获取源码的语义信息，主要包括以下三个步骤：a) 去除源码中多余的换行符、注释等无关信息；b) 将源码解析AST;  c)扩展AST节点信息，通过对 AST 自顶向下的遍历，将子节点信息添加到父节点中，以获取 AST 的上下文信息。
C.基于字典的同义词转换
基于字典的同义词转换旨在通过标准化不同编程语言中的抽象语法树（AST）节点表示，消减语言间的语法差异。通过构建一个包含不同编程语言节点类型和节点值同义词映射的字典，将具有相同语义的术语统一为标准化术语。在处理AST时，根据该字典将不同语言中的节点类型和节点值进行替换，从而形成具有一致语义的统一AST。此方法有效地减少了由于语言特性差异导致的检测误差，提高了跨语言代码克隆检测的准确性和鲁棒性。
D.代码克隆检测模型
跨语言的代码克隆检测主要分为四层，分别为嵌入层、卷积层、池化层和对比层:
a) 嵌入层
通过将AST节点的类型和节点值转化为向量表示，提供了对代码语法和语义的深层次理解。它通过构建节点的词汇表和嵌入矩阵，将不同编程语言中的节点信息标准化为统一的向量形式，从而减少语言间差异，为后续的卷积层和对比学习层提供了丰富的特征输入。这一过程不仅提升了模型对节点特征的捕捉能力，还支持了跨语言代码克隆检测的准确性和有效性。
b) 卷积层
通过应用树卷积神经网络对AST进行卷积操作，提取节点及其邻接节点的局部特征。卷积层利用预定义的卷积核对AST中的节点特征进行加权求和，整合了节点的局部信息和上下文关系，从而生成全面的特征表示。这些特征向量不仅捕捉了节点的语法结构，还反映了其在整体代码结构中的角色，为后续的池化层提供了丰富的特征输入，提升了模型对代码模式的识别能力。
c) 池化层
通过对卷积层输出的特征向量应用最大池化操作，从而汇总整个AST的局部特征为一个全局特征向量。该池化层通过选取每个特征维度上的最大值来简化特征表示，保留最显著的特征信息，并将其作为后续对比学习层的输入。这一过程有效地减少了特征维度，提升了模型对全局结构特征的捕捉能力，增强了代码克隆检测的准确性和鲁棒性。
d) 对比层
利用对比学习的损失函数对AST的向量表示进行优化，通过最小化正样本之间的距离和最大化负样本之间的距离，从而提高模型区分克隆对和非克隆对的能力。该层通过计算向量之间的相似度并应用N-pair损失函数，优化模型对克隆代码的识别精度，并最终将这些优化后的特征用于判断代码对是否存在克隆关系。这种方法提升了模型的泛化能力和检测的准确性。
(2)基于关键阻塞链识别的漏洞修复传播路径分析方法研究
如下图所示，是本研究内容的具体方法模型。

图 8 关键阻塞链识别的漏洞修复传播路径分析
A.监控包管理系统生态系统的演变
为了准确捕捉整个生态系统中漏洞修复的传播情况，该方法持续收集包管理系统包的漏洞元数据和依赖元数据。通过比较两个连续的包管理系统依赖元数据快照，还可以挖掘包的迁移历史，为制定修复策略提供线索。
B.元数据建模与收集
将包管理系统快照的元数据建模为一个 2 元组 M(si) = (dmi, Gi):
是已披露漏洞的集合，其中。字段Idk和 slk表示漏洞µk的唯一标识符和严重级别。字段 pk 和 verk = {v1,v2,··· ,vn}代表受该漏洞影响的包名及其发布的版本。
Gi = (Vi,Ei,Ci)是描述包管理系统快照 si依赖元数据的有向图。Vi表示在快照si上发布的包版本集合。Ei = {pk@va → pu@vb|pk@va,pu@vb ∈ Vi}  表示包版本之间的依赖关系集合。每个依赖关系pk@va → pu@vb ∈ Ei由其对应的依赖约束 c(pk@va,pu) ∈ Ci,决定，其中 c(pk@va,pu)表示pk@va对 pu指定的版本约束，vb是在快照 si上满足该约束的pu的最高版本（符合包管理系统的依赖解析规则）。
该方法从三个具有代表性的数据库——GitHub Advisory DB、Snyk Vulnerability DB 和 NPM Security Advisories——爬取上述漏洞元数据。而依赖元数据 Gi则借助包管理系统公共注册表提供的 RESTful API 进行爬取。本方法每周更新元数据模型M(Si)，以持续监控漏洞和包管理系统依赖元数据的演变。为分析漏洞修复在包管理系统生态系统中的传播情况，dmi只记录已通过修复补救的漏洞。
C.挖掘包迁移历史
在演化过程中，该方法通过比较两个连续包管理系统快照 si和 si+1 的依赖图 GGG，识别包的迁移记录，从而为制定修复策略提供线索。正式定义包迁移记录如下：
定义包迁移记录：如果一个项目弃用包并采用包 作为替代，将定义为迁移记录，其中是源包，而是迁移中的目标包。
结合库迁移挖掘方法和收集的依赖元数据，该方法根据两个标准收集包迁移记录：
如果在快照中存在，而在快照中，废弃了包 并依赖于新包，本方法将记录为依赖变化对。如果这样的依赖变化对在不同项目中出现超过 M 次，本方法认为是可能的迁移记录。本方法的默认值为 M = 5，这可以通过实验验证有效地减少噪声。将其自适应调优留待未来工作。
对于每个可能的迁移记录，为了保证其可靠性，本方法使用关键字“replace ” OR “deprecate ” OR “remove ” OR “switch ” OR “migrate ” OR “delete ” 在 GitHub 上搜索其对应的迁移提交。本方法将 确定为某个迁移记录，当且仅当在返回的代码提交中能够识别出新添加的包 。值得注意的是，对于源包，本方法可能会在迁移记录中找到多个目标包。在推导补救策略 C 时，可以为开发人员提供所有可能的建议，以迁移有问题的包。
D.识别关键阻塞链
本方法构建的元数据模型 M(si) = (dmi, Gi)包含了识别脆弱路径和阻塞链所需的充分信息。
E.识别脆弱路径
设为受  中的漏洞影响的脆弱包版本集合， 为最新的活跃包版本集合（即过去一年发布的版本）。在依赖图上，本方法将包版本视为根节点，将脆弱包版本视为叶节点，通过可达性分析识别所有脆弱路径。注意，本方法仅考虑根节点为最新版本的活跃包的脆弱路径，以分析漏洞对整个生态系统的现状影响。
然而，一个包的版本可能包含多个漏洞，每个漏洞对应于一个特定的安全版本集。例如，Dotty 0.1.0 包含两个漏洞 CVE-2021-23624 [48] 和 CVE-2021-25912。对于这两个漏洞，Dotty 的安全版本分别是 {≥ 0.1.1} 和 {≥ 0.1.2}。为了准确找到阻塞包，在识别漏洞路径时，一个包含多个漏洞的包版本会被区分为多个叶节点。通过这种方式，本方法确保一个漏洞路径涉及一个唯一的漏洞。
F.识别关键阻塞链
对于每个漏洞路径，从漏洞包开始，本方法迭代计算每个包的漏洞 μt 的安全版本，直到找到一个阻塞包  使得。然后，本方法将  视为一个阻塞链。
具体而言，每个包的安全版本是这样确定的：
对于漏洞包，本方法记录其安全版本集为，其中表示在包管理系统快照中发布的版本集合，记录了受 影响的 的漏洞版本。
于漏洞路径上的其他包 ，本方法使用函数 ResolvedSV 来计算其安全版本集。假设 是  在漏洞路径上的直接依赖。ResolvedSV 返回依赖于安全版本 的  的版本，或淘汰包的  的版本。
最后，本方法根据通过这些阻塞链的漏洞路径数量对识别出的阻塞链进行排序。排名最高的阻塞链被视为关键链，需要修复以便将漏洞修复传播到大量的包中。
2.5.1.3 二进制代码软件成分分析技术研究
(1)基于文件字节特征的二进制启发式解包技术研究
在当前信创环境中，大多数软件以二进制代码形式发布，针对不同处理器架构、操作系统、定制化需求以及行业领域的差异，二进制软件包的封装格式多样化。现有的二进制解包技术主要面向通用格式，如压缩包、镜像文件等，但对于复杂和多样化的场景（如安装包、固件、数据包、多重封装格式等），传统基于文件后缀和文件头部字节（魔数）的识别方法已经显得力不从心。这种局限性导致文件格式无法被正确识别，进一步阻碍了二进制文件内容的深入分析，尤其是面对行业专有格式、加壳格式及复杂的多重封装文件时，现有技术的处理能力仍显不足。
本项目提出了一种基于文件字节特征的二进制启发式解包技术，旨在通过对不同文件格式字节特征的分析和学习，提升复杂二进制文件的解包能力。其关键步骤如下：
(1)字节特征库的构建：收集各种类型文件的字节特征，建立与文件格式对应的字节特征数据库。该数据库涵盖可执行文件、压缩文件、镜像文件、固件、图片等多种文件格式。通过系统化的特征收集，确保广泛支持常见和行业专有的二进制格式。
(2)启发式分析与标记：研究能够对二进制文件内的字节序列进行深入分析的技术，根据字节特征库中的数据，识别并标记文件格式在二进制文件中的起始和结束位置。基于这些标记，启发式地确定文件格式，即便在文件后缀或头部信息缺失或被混淆的情况下，也能够准确识别文件结构。
(3)深度解包与多重封装处理:对于标记出的文件格式，按照起始和结束位置切割出独立的文件。进一步研究自动化技术，针对加壳、压缩及多重封装格式的文件，进行自动脱壳和解压缩，从而实现二进制文件的深度解包。通过这些处理步骤，能够有效地应对复杂格式、多层封装的二进制文件，提升分析的深度和广度。

图 9 二进制文件启发式解包示意图
如上图是二进制文件启发式解包示意图。这种基于字节特征的启发式解包技术，突破了传统方法的局限，能够应对更加复杂和多样的二进制格式，特别是在行业专有格式和定制化封装格式的场景下，具备更强的灵活性和适应性，为二进制文件的成分分析提供了有效支持。
(2)基于深度学习的二进制函数向量相似度模型技术研究
本项目将致力于研究基于多层卷积神经网络（CNN）架构的深度学习AI模型，专注于函数特征语义嵌入的相似性分析。通过将函数基本块中的底层特征、属性控制流程图（CFG）以及基本块之间的数据流转关系融入模型，最终实现每个二进制函数的向量化嵌入，并存储到向量数据库中。该方案的核心优势如下：
(1)图结构与孪生网络的结合：项目采用基于图结构的模型训练方法，并引入Triplet Network孪生网络。这一设计能够携带更多函数的语义信息，提高模型对不同场景下函数相似性的鲁棒性，确保在面对多样化输入时，依然能保持较高的准确性与适应性。
(2)多维度的信息融合：通过结合函数特征语义、函数基本块、属性控制流以及数据流等多个维度进行信息融合，模型可以更全面地捕捉函数的特征，即使在信创跨平台、跨架构的复杂异构场景下，依然能够保持较高的相似性判断精度。此设计使得模型在多样性和复杂性显著增加的环境中仍具有较强的泛化能力。
(3)高效的向量表示与检索：每个函数通过深度学习模型进行向量化表示，使得计算速度大幅提升。通过引入近似最近邻搜索算法 HNSW（Hierarchical Navigable Small World），模型在处理海量函数向量数据时，依然能实现毫秒级响应。这不仅保证了在亿级函数向量数据库中的高效检索，还极大地提升了系统的实用性和扩展性。
如下图所示，同一段函数代码，经过不同的编译器、编译参数、操作系统平台以及处理器架构的处理后，可能生成多种二进制变种。对于任意两个变种，借助大型模型的判断，都可以输出其相似性。

图 10 二进制函数代码变种
(3)面向函数内联场景的二进制到源代码函数相似性检测方法研究
二进制到源代码相似性检测方法广泛应用于包括代码搜索，开源软件复用检测,以及软件组成成分分析在内的多个领域。已有的方法通常认为复用函数之间包含了等价的函数语义,因此通常使用一对一的匹配方式来对函数进行比较。
然而，复用函数之间并不一定总是承载着等价的函数语义。当函数内联发生时，一个二进制函数通常由两个或者多个源代码函数编译生成。此时，若二进制函数在编译过程内联了源代码函数，查询函数和目标函数之间的映射关系将会从一对一转变为一对多。函数内联广泛存在于二进制文件中，当使用O3优化级别时，接近40%的二进制函数是通过内联生成的。而现有的二进制到源代码相似性检测方法在处理含内联的二进制函数时，性能损失高达30%。因此急需解决函数内联带来的“一对多”的匹配问题。
针对上述问题,本项目提出了一种采用“一对多”的匹配机制来匹配二进制函数与源代码函数的方法。下图展示了本方法的核心思想，总体而言，传统的“一对一”匹配方法通过比较二进制函数和源代码函数得到复用结果，而本方法旨在生成源代码函数集合，用以补充源代码函数的缺失，从而来与内联的二进制函数进行对比。源代码函数集合是一个由多个源代码函数组成的函数集合，该源代码函数集合包含了该二进制函数对应的源函数以及参与内联的其它源函数，因此其语义等同于内联后的二进制函数。从而帮助现有技术在函数内联的情况下进行二进制到源代码函数相似性的匹配。

图 11 “一对多”匹配机制
方法的工作流程如下图所示分为三个部分：特征提取、内联函数调用预测及源代码函数集合生成。

图 12 总体工作流程图
A.特征抽取
通常来讲,编译器一般通过衡量内联某个函数调用的成本和收益来决定是否进行内联.所选特征列于表2中.
表 2 内联函数调用预测预测所选特征
对象	部分	特征
调用函数与被调用函数	函数体	语句总数，
While语句数,
Switch语句数,
Case语句数,
If语句数, 
For语句数, 
返回语句数, 
声明语句数, 
表达式语句数
	函数定义	Inline关键字数量,
Static关键字数量
	函数调用	调用次数,
被调用次数
调用指令	位置	路径长度,
是否位于For循环内,
是否位于While循环内,是否位于Switch语句内,是否位于If条件内
	参数	参数总数量, 
常量参数数量
特征主要来自于两个部分：调用函数与被调用函数,以及调用指令.
调用函数与被调用函数的属性会影响内联被调用函数的成本和收益.成本主要来源于函数内联带来的二进制文件的体积膨胀,而收益主要来源于内联所减少的函数调用开销.本节将从函数体、函数定义及函数调用三个部分出发,介绍本文所选取的用于内联函数调用预测的特征.
在函数体中,本方法提取了不同类型的指令数量作为特征.例如,语句总数表示所有语句的数量,而While语句数表示while循环语句的数量.这些指令的计数代表了函数的体积大小,这表明了内联该函数的成本.简单来说,被调用函数越复杂,内联该函数调用的成本就越高.
在函数定义中,本方法提取了“Inline”和“Static”两个重要关键字的使用次数作为特征.关键字“Inline”是对编译器的建议,表明当其他函数调用此函数时,应将其内联.由“Static”修饰的函数仅可由同一编译单元中的其他函数访问.这些关键字直接或间接地影响编译器的内联决策.
在函数调用中,本方法提取了调用函数与被调用函数的调用次数和被调用次数作为特征.直观上,如果一个被调用函数被众多调用者函数调用,将其内联到所有调用者中的成本会随着调用者数量的增加而增加.相反地,当一个函数仅被调用一次时,内联只会带来优化的好处而不会增加代码大小,这是进行内联的理想情况.
对于调用指令,调用指令的位置和函数调用的参数信息是影响函数内联实施的两个重要因素.例如,如果一个函数调用位于由“for”或“while”定义的循环中,内联该函数调用将显著减少函数调用的时间.此外,如果一个函数调用包含常量参数,并且这个参数能帮助确定被调用函数中的某些分支,那么内联该函数只需要内联对应的分支内容,这也会使内联这些函数的成本有所降低.
B.内联函数调用预测
内联函数调用预测旨在预测开源软件中的内联函数调用,主要包括两个部分：模型训练和模型测试.从流程上来讲,本节首先设计一个分类器并在训练数据集上对其进行训练,然后使用这个分类器来预测测试数据集中的函数调用的标签.
1)模型训练
不同的编译器家族和不同的优化级别会导致不同的内联函数调用.考虑到在每种编译设置下,每个函数调用都将有一个相应的内联标签,本方法将内联函数调用预测问题视为一个多标签分类（Multi-Label Classification, MLC）问题.
考虑到内联决策间的关联性,本方法设计了一个名为ECOCCJ48 (Ensemble of Compiler Optimization based Classifier Chains built with J48)的多标签分类器,它使用二元关联（Binary Relevance）为不同编译器家族预测标签,并使用分类器链(Classifier Chains)为不同优化级别预测标签.
     
（a）编译器级别 					（b）优化选项级别
图 13 ECOCCJ48架构图
如上图(a)所示,编译器级别的分类器为GCC和Clang编译器家族分别设计了对应的优化级别分类器,这两个分类器分别独立训练（二元关联）.由于同一编译器家族的编译器做出类似的内联决策,ECOCCJ48整合了同一编译器家族下的函数调用,标签数量将从32减少到8（2种编译器家族*4种优化级别）.
在每个优化级别分类器中,标签在不同优化级别间存在序列依赖,如图6(b)所示.例如,O2中进行内联函数调用预测的输入是函数调用特征及已为O0和O1预测的标签.考虑到在O0和O1中进行的内联决策通常也会出现在O2中,ECOCCJ48的架构可以利用优化间的内联关联性来产生更准确的预测结果.
此外,本方法还使用集成方法(Ensemble Methods)来增强分类器的性能.在性能方面,集成学习方法要优于单模型学习方法.本方法首先在随机选取的训练数据集上训练基分类器,然后通过聚合基分类器的预测来预测标签.由于基本分类器可以在不同的语料库上进行训练,它们能够捕捉到一些稀有的内联模式.
2)模型预测
在模式测试阶段,给定一个开源软件项目,本方法首先提取所有函数调用并构建其函数调用图（Function Call Graph, FCG）.然后,对于FCG中的每个函数调用,本方法提取相应的特征.将这些特征作为输入到ECOCCJ48中,最后得到每个本方法在所有编译设置下的标签.获取每个函数调用的标签后,本方法创建与8种编译设置相对应的8个FCG.然后,在这些8个FCG中标记内联的函数调用.最后,将得到8个有标签的FCG用于进一步的源代码函数集合生成.
C.源代码函数集合生成
在获得标记的FCG后,需要在其中生成相应的源代码函数集合.下图(a)展示了一个有标签的FCG示例.图中用红色边表示内联调用,蓝色边表示普通调用.红色圆圈代表有内联调用的函数,黑色圆圈代表仅有普通调用的函数.
本方法将源代码函数集合生成的过程转化为包含根节点选择和边扩展的内联函数调用图的生成问题.根节点选择目的在于选择可以作为主函数的源代码函数节点,而边扩展则是将每个内联函数添加至主函数中,以生成对应的源代码函数集合.
  			   		  
（a）有标签的FCG 	  		（b）根节点选取		 	（c）调用边扩展
图 14  源代码函数集合生成示例
1)根节点选择
首先,本文将内联子图定义为由内联调用及其关联函数构成的FCG的子图,只要内联子图中的节点能满足以下两个条件之一,它就可以作为根节点来生成子树.
节点是内联子图的根节点.
节点是非根节点,它有到其他节点的内联调用,并且还有其他节点通过普通调用连接到它.
2)调用边扩展
调用边扩展同样遵循两条规则.
如果调用者和被调者之间只有内联边,则遍历内联子图中根节点可达的所有节点.
如果调用者与被调者之间同时存在内联边和普通边,则生成两种版本的源代码函数集合.一种沿着内联边继续包含后续节点,另一种在遇到普通边时停止.
如果内联子图中存在循环,本方法会记录已遍历的节点并在遇到已记录的节点时停止探索该路径.得到源代码函数集合后,本方法使用Understand进行源代码函数内联,针对每个源代码函数集合生成最终含内联的源代码函数,以作为内联二进制函数匹配的目标.
(4)基于模块化语义匹配的二进制代码第三方库模块检测方法研究
随着软件规模和复杂度的快速增长，使用第三方库(TPLs)已经变得越来越流行。现有的大多数方法依赖于句法特征识别第三方库模块，当这些特征发生变化或被对手蓄意隐藏时，这些特征的鲁棒性不强。此外，这些方法通常将每个导入的库建模为一个整体，无法应用于软件仅部分使用库代码段的场景。为了在语义级别检测完全和部分导入的TPLs，本项目将研究利用新的程序模块化技术将程序分解为细粒度的基于功能的模块的框架，通过同时提取模块的语法和语义特征，度量模块间的距离，检测程序中相似库模块的复用。
下图显示了方法的工作流程。包含两个阶段，分别是二进制模块化和第三方库（TPL）检测，以从二进制程序中预测 TPL。在第一阶段，提出了一个模块质量指标，该指标基于社区检测算法并结合了程序的特定调整。随后，利用带有启发式偏差的算法，根据该指标将二进制程序分解为多个模块。在第二阶段，通过将程序模块与 TPL 模块进行匹配来执行 TPL 检测。通过提取语法特征、图拓扑特征和函数级别特征，以衡量模块之间的相似性。匹配完成后，还引入了模块和库的重要性评分，以提高库检测的准确性。
A.二进制程序模块化
程序模块化技术由两个部分组成：模块质量指标和实际的模块化算法。模块指标旨在衡量将函数分组到簇中所带来的质量提升，而模块化算法则以最大化整体模块质量得分的方式组合这些函数。

图 15 方法整体框架图
1)模块质量评估设计
本方法采用了社区检测质量指标作为基线。随后，结合软件特定的启发式方法对这些指标进行了修改，以适应特定的程序模块化任务。
方法选择了Girvan–Newman 模块化质量 (GN-MQ) 作为基线指标，给定一个已被划分为多个簇的网络，该指标计算同一簇中每对节点之间的连接边，并根据节点度的自适应权重累加这些连接出现的次数。如果同一簇中的节点之间没有连接，则该权重将被赋予负值，从而降低整体质量得分。
除了节点之间的连通性，程序模块还有一些独特的特征，可以用作模块质量指标。函数体积就是其中之一，它由函数中的语句数量来指定。在程序中，体积较大的函数通常执行一些核心功能，而体积较小的函数往往是辅助函数。一个完整且连贯的程序模块将包括一小组大体积函数来执行核心功能，以及一些小体积函数，这些小体积函数在核心组周围提供有用的工具。因此，本文提出了函数体积权重传播算法，以在度量中加入权重调整，从而使其更倾向于完整且连贯的模块。对于具有层次结构的程序，顶层的函数往往通过函数调用控制低层函数的行为。传播算法确保顶层函数会比低层函数获得更多关注，从而将更多的权重分配给顶层函数。
除了调整体积大小外，本方法还将度量标准从测量无向图改为测量有向图，因为函数调用具有方向性（从调用者到被调用者函数）。具体而言，只测量父节点的出度和子节点的入度，以避免其他无关调用边带来的噪声。
2)模块化算法
基于提出的模块质量评分，将对程序中的函数进行分组以生成模块。将每个函数视为一个独立的集群，并使用快速展开算法重复地合并两个集群，同时最大化整体质量评分。此外，为了使生成的模块更具直观性，在模块化过程中加入了两个偏置来引导过程。为了提高模块化速度，选择了快速展开的 Louvain 算法，这是一种贪心优化算法，用于指导分组过程。
在程序开发过程中，设计用于执行相同任务的函数可能会被放置在一起（例如，在同一个源文件中）。因此，在编译成二进制可执行文件后，这些函数将一个接一个地放置。利用这一启发式方法，方法将局部性偏置引入到模块化算法中。关键思想是，我们希望将彼此接近的函数分组，因为它们更有可能执行相同的任务。为此，每个函数根据其在二进制中的位置序列分配一个索引号。根据单一职责原则，每个方法或模块应具有单一功能，该功能应由其封装。方法希望模块具有有限的入口，以确保其功能单一且封装。因此，在模块化过程中引入了入口偏置指标。在本研究中，模块入口定义为只有其调用函数在模块之外的函数节点。

图 16 第三方库模块检测流程
B.第三方库检测
在对程序和第三方库进行模块化之后，本方法提出了一种相似性测量算法，以基于语法和语义特征匹配模块，并检测程序中的第三方库。上图展示了通过模块匹配进行第三方库检测的流程。
1)模块相似度度量
在模块相似性测量中，使用字符串字面量和常量数字作为语法特征。字符串字面量是最重要的特征，因为它通常具有唯一的值，容易被区分。如果两个模块中的两个函数具有相同的字符串字面量，它们很可能是相同的函数。然而，只有一小部分函数具有字符串字面量。因此，字符串只能帮助我们准确匹配一些函数和模块。与字符串字面量相比，常量的唯一性较低。为了解决这个问题，采用TF-IDF算法，为更具唯一性的常量分配更多的权重。
模块由互相调用的函数组成，形成一个调用图。使用传播图核算法来测量调用图的相似性。该算法试图测量两个图之间的图结构和子图结构的相似性。对于更细粒度的特征，采用了RouAlign中的边嵌入方法来测量拓扑中的边相似性，将特定模块的边嵌入为向量。然后，通过向量搜索来找出图的相似部分，这些特征用于测量模块中函数之间的相似性。由于一个模块由多个函数组成，得分将被汇总以测量模块相似性。
为了计算得分，本方法利用一种先进的二进制函数匹配工具Gemini来生成两个给定函数之间的相似性得分。此外，一个模块可能包含具有不同功能的函数，由于每个模块包含多个函数，逐对计算函数相似性需要时间。因此，将采用基于启发式规则的方法来选择函数对。相似的函数通常使用相同的数据组；或者它们会被相同的指针引用。
2)第三方库检测
方法通过检查目标程序中的模块是否可以与签名TPL数据库中的任何模块匹配来执行第三方库检测。对于目标程序中的每个模块，将其与签名TPL数据库中生成的所有模块进行匹配。根据相似性得分对候选模块进行排名，并选择具有高且可区分的相似性的模块。
为了进一步提高准确性，引入了模块重要性（MI）得分，以选择被认为更重要的模块。在启发式方法中，认为模块的大小越大，模块的重要性就越高。因为较大的模块往往具有更多独特的结构，不容易与其他模块匹配。其次，对于一个库，其重要性应该与参考频率正相关，与库包含的模块数量负相关。一个库被其他二进制文件需要的频率越高，库的模块数量越少，如果库的模块在程序中被检测到，那么它应该越重要。基于这一假设，给予模块的匹配置信度得分。更高的匹配置信度得分表示对模块检测的可信度更高。最后，将相似性得分与匹配置信度结合，输出最终的TPL检测结果。
2.5.2 针对异构复杂软件供应链的风险评估体系技术研究
面向异构复杂软件供应链潜在风险量化评估需求，本课题围绕海量异构复杂供应链软件风险评估所需的通用性、高效性、安全性和可靠性等要求，研究多源异构供应链软件威胁建模技术、多维度的风险指标体系构建方法、基于源代码漏洞可达性分析技术以及二进制软件包多维度分析评估技术。具体的研究技术路线和技术方案如图 17所示。

图 17 课题二技术路线图
2.5.2.1 多源异构供应链软件威胁建模技术研究
本课题研究的异构供应链软件数据来源广泛且结构复杂，各个行业不同厂商所使用的许可证标准各不相同且存在违规使用许可证的情况。为应对上述问题，课题首先针对许可证的检测问题提出了一种违规许可证数据检出技术，并针对数据差异化的问题提出了一种灵活的差异校准机制，用于处理不同数据源和结构之间的差异。在数据差异化校准的基础上，构建供应链软件威胁模型。

图 18 多源异构供应链软件威胁建模技术路线图
A)违规许可证检出技术
（1）许可证兼容性建模
由于不同许可真之间的兼容关系复杂，难以提取共性，本课题通过构建有向无环图（DAG）来系统地表示和分析开源软件许可证的兼容性。将每个开源许可证及其版本表示为图中的一个节点（顶点），并通过有向边（edges）连接具备“单向兼容性”的许可证。在包含多个不同许可证的代码时，代码可以在图路径终点的目标许可证下发布。
1）单向兼容性
在许可证V1指向许可证V2的情况下，如果许可证V1与许可证V2兼容，那么在使用了两者的代码时，可以在V2下发布。这意味着V2通常是一个更严格的许可证，确保所有包含V1的代码也满足V2的要求。
2）许可证选择
为了构建兼容性图，方案选择了20种最常用的开源许可证。这些许可证被认为是业内最流行的，并且都经过了开放源代码促进会（OSI）和自由软件基金会（FSF）的批准。这些许可证涵盖了不同的类别，如宽松许可、弱保护性许可和强保护性许可。
3）兼容性分析
通过参考多种资源，包括维基百科、许可证文本、论坛讨论，以及GNU网站上的GPL兼容性指南，对这些许可证的兼容性进行了深入分析。特别地，它分析了当多个许可证组合在一起时，哪些许可条件是可以共存的，哪些组合是冲突的。例如，虽然MIT和BSD许可证看似等效，但由于BSD可能包含更多的限制性条款，最终合并代码需要在更严格的BSD许可证下发布。
4）有向无环图（DAG）的构建
在构建许可证兼容性图时，采用有向无环图的结构。图中的每个顶点代表一个具体的许可证版本，每条边表示许可证之间的兼容性关系。由于是单向兼容，边的方向性保证了从较宽松的许可证向更严格的许可证过渡，而不会回到更宽松的许可条件。例如，公共领域的代码（Public Domain）可以过渡到宽松许可（如MIT许可证），但无法反向转换。
（2）许可证违规检查器
许可证违规检查器旨在自动化地验证许可证文件中的许可证声明，检测可能的违规行为，并提供必要的调整建议。具体包括：1）使用FOSSology许可证分析工具从软件包中提取许可证信息并生成SPDX文件；2）通过Tag to RDF Translator将SPDX文件转换为RDF格式；3）通过Floyd-Warshall算法构建许可证图的传递闭包；4）使用许可证图检查SPDX文件中声明的许可证是否兼容，并提供违规调整建议。
B) 灵活差异校准机制
所提出的灵活差异校准机制包括数据标准化、数据融合富集、差异校准及数据归一化。具体如下：
数据标准化：收集的异构供应链软件数据需遵循完整性和规范性原则。通过全面的数据收集与融合富集算法，围绕目标对象构建完整的属性集合，确保对研究对象特性描述的全面性和准确性。同时，对目标对象的命名规则、格式约定、别名机制、数据结构、编码规范、输入输出、注释等方面进行统一规范，以确保校准机制的权威性和一致性。
数据融合富集：处理多模态、多层级数据，包括软件本身的信息、运行环境、操作系统、网络、服务器等，整合不同层次和粒度的数据，确保所有数据指向同一对象，增强数据的完整性和表达能力。
灵活差异校准：针对不同类型和来源的数据，制定差异校准策略和合并算法，确保目标对象的意思表示一致，解决多数据源间的冲突与差异。
数据归一化：为支持后续的算法分析，需要对校准后的数据进行归一化处理。通过特征分析（如最大值、最小值缩放处理），将特征值统一缩放到标准区间，消除不同数据指标之间的量纲差异，确保它们的可比性。
C) 供应链软件威胁建模
威胁建模作为一种结构化方法，可以用来识别、量化并应对威胁，利用抽象的方法来帮助思考风险，可以帮助确定软件在整个生命周期中面临的威胁、攻击、漏洞等，从而帮助供方和使用方及时发现风险，制定相应措施，确保软件安全。常见的威胁建模方法有威胁6要素STRIDE、威胁树、攻击图等方法。其中，威胁树通过树形结构描述对资产的各种攻击，用根节点表示最终的攻击目标，逐层细化威胁，直到用叶节点表示具体的攻击方式。建模方法包括创建威胁树、标识威胁、威胁量化、识别主要攻击路径、威胁消减等步骤。
为了定量分析异构复杂软件供应链面临的安全威胁，本课题从攻击者的角度建立软件供应链的威胁树模型，以加强对软件供应链的安全风险管理。软件供应链威胁树模型如下图所示，其中G为根节点，M为中间节点，L为叶节点，各节点的含义如下表所示，其中G代表攻击的最终目标，即软件供应链攻击。G的子节点包括M1-M5的中间节点。

图 19 软件供应链威胁树模型
	其中各节点含义信息如下：
（1）恶意篡改（M1）
表示在软件供应链的设计、开发、生产、集成等环节，对软件产品或组件进行恶意篡改、植入、替换等，以便嵌入包含恶意逻辑的软件或硬件。M1的具体攻击手段包括植入恶意程序（L1）、植入硬件木马（L2）、篡改第三方组件（L3）、未经授权的配置（L4）、篡改供应信息（L5）等叶节点。
（2）假冒伪劣（M2）
表示软件产品或组件存在侵犯知识产权、质量低劣等问题，具体包括不合格产品（L6）、未经授权的生产（L7）、假冒产品（L8）等叶节点。
（3）供应中断（M3）
表示由于自然或人为因素造成的软件产品供应量或质量的下降，甚至导致软件供应链中断，包括突发事件中断（L9）、基础设施中断（L10）、不正当竞争（L11）、组件停止生产（L12）等叶节点。
（4）信息泄露（M4）
表示软件供应链上传递的敏感信息、关键数据等非法泄露，包括共享信息泄露（L13）、商业秘密泄露（L14）、信息窃取（L15）等叶节点。
（5）违规操作（M5）
表示软件供方的违规操作行为，包括违规收集和使用用户数据（L16）、滥用大数据分析（L17）、影响市场秩序（L18）等叶节点。
为了分析叶节点攻击成功的概率，定义Pm为父节点实现的概率，、、…、为其下各子节点的概率，各子节点之间为OR关系，即任意一个或多个子节点攻击成功都代表其上的父节点攻击成功，且有：
 
即父节点攻击成功的概率为其下各子节点攻击成功概率中的最大值。
对于每个叶节点定义安全评价指标，即安全属性A，且，其中C为攻击成本，D为攻击难度，F为攻击被发现的可能性。为了定量分析叶节点的风险，定义叶节点攻击实现的概率为：

其中、、分别表示攻击成本C、攻击难度D和攻击被发现可能性F的权值，且有；分别表示3个安全属性的效用值，且对应与C、D、F成反比关系，为了便于计算，本课题取。
首先需要采用专家打分的方式对每个叶节点的3个安全属性进行评估，具体评价标准见下表。为保证打分的客观性和专业性，需要选择不同行业或者同一行业内不同领域的3-5位专家进行打分。
表 3 安全属性评价指标

在打分过程中为避免受到个人意识、认知等主观因素的影响，采用模糊层次分析法（FAHP）计算安全属性的权值。FAHP是利用元素之间的逐一比较结果构成模糊一致矩阵，再计算各元素相对重要性权重的方法。
基于FAHP方法计算元素相对重要性权重的方法是：参照尺度表对元素之间两两比较后构造出模糊判断矩阵C，如果C满足公式1，则C是模糊一致矩阵；如果不满足公式1，需要按照公式2转换成模糊一致矩阵，最后利用式公式3求出各元素相对重要性的权值。
公式1：
公式2：
公式3：
基于以上方法对威胁树中每个叶节点进行威胁量化，完成供应链威胁建模。
2.5.2.2 多维度风险指标体系构建方法研究
本课题从技术、管理、知识产权、可维护性和闭源组件等多个维度对异构复杂软件供应链的安全风险进行全面评估，并构建了相应的风险评估指标体系，如下图所示。在具体评估过程中，本课题依据软件组件的来源（如开源、闭源）选择相应的评估指标。针对闭源组件信息缺乏、有效评估指标不足的特点，综合代码特征与二进制代码成分分析结果，额外设计二进制代码维度下的评估指标，进行针对性的风险评估，以确保评估结果的准确性和全面性。

图 20 多维度风险评估指标体系图
A）软件技术评估维度
技术类指标以软件生命周期为主线，从软件设计、编码、构建、测试、发布和运维6个阶段进行评估。
（1）设计阶段
该阶段主要进行软件组件选取和软件组件设计，软件组件设计可通过软件组件技术文档体现，在各软件组件中具体评估。软件组件选取主要从软件组件的来源、许可信息、所属社区运营状态、是否有替代产品以及能否实现自主演化等方面考察软件组件的可溯性与可用性。
（2）编码阶段
在各软件组件的编码阶段，因为内部开发团队不同而存在一定差异，不便统一评估，所以在评估结构组成风险时，对单个软件组件编码阶段的风险进行单独评估。
（3）构建阶段
该阶段共分为两步，一是将源码编译构建生成可安装运行的软件组件，二是将相关软件组件进行版本构建生成镜像文件。软件组件构建工具（如debuild、pbuilder等）和版本构建工具（如pungi、OBS等）在构建过程中可能影响软件内容，存在植入和篡改风险。在构建时，配置文件中的开发人员、版本号、许可证和依赖关系等信息是否详尽准确，这将影响产品的可溯性，可通过构建工具和配置文件来评估构建阶段的风险。
（4）测试阶段
根据测试方案，本项目使用测试工具对软件组件进行功能性和安全性测试。测试阶段发现潜在风险的能力取决于测试方案的完备性和测试工具的性能。然而，测试工具也可能对软件内容产生影响，存在植入或篡改的风险。为评估测试阶段的风险，需要关注测试方案的完备性、测试工具的性能以及安全性。
（5）发布阶段
通过特定渠道将系统镜像和软件组件发布，并交付给用户。如果发布渠道受到攻击，发布的软件内容可能存在被植入或篡改的风险，在发布时进行正确性验证可以在一定程度上降低这些风险。为评估发布阶段的风险，需要关注发布渠道的安全性及发布时的正确性验证手段。
（6）运维阶段
该阶段需要及时修复发现的安全风险并发布升级补丁，主要包括以下3个环节：首先，及时发现并收集安全风险信息；然后，快速响应并及时修复；最后，通过升级渠道将补丁部署到用户端。软件安全受安全风险信息收集的及时性和全面性、响应速度以及升级渠道安全性的影响。本课题主要通过风险信息收集和升级渠道安全性两个指标来评估运维阶段的风险。
B）软件管理评估维度
在评估软件组件的运营风险时，需要具体分析其结构组成风险。组织的运营风险会从整体上影响软件供应链的安全性，一般通过组织背景判断组织的持续经营能力，根据市场管理评估产品的竞争力。在评估可溯性风险、知识产权风险和安全风险时，不仅要关注技术方面的风险因素，还需要考虑管理方面的风险因素。这些风险可以通过供应链管理、知识产权管理和安全管理来进行评估。
（1）组织背景
组织背景在评估组织持续运营能力时起着关键作用，主要考虑核心团队、盈利能力和技术积累时间3个方面。稳定的核心团队、较强的盈利能力以及较长的技术积累时间通常意味着组织可持续运营能力较强，从而降低了运营风险。
（2）安全管理
安全管理主要从以下3个方面评估软件的安全保障能力：安全管理组织、安全开发流程和安全事件响应策略。安全管理组织的建立决定了组织内是否有专责人员关注并负责安全事务；安全开发流程确保了产品开发过程中的安全性；安全事件响应策略是指安全事件发生后采取的措施，可以有效降低安全事件对软件造成的影响。
（3）供应链管理
供应链管理主要从供应方管理、配置管理和工具管理3个方面评估人和组织、代码和组件以及工具的可溯性与可用性。供应方管理关注是否掌握向软件提供内容的开源社区和内部开发者的情况；配置管理主要看是否掌握源代码和软件组件的版本迭代以及供应方的相关信息；工具管理主要评估是否掌握系统生命周期过程中使用的各种工具及其来源，同时跟踪掌握其当前可用状态。
（4）产权管理
产权管理主要从知识产权管理组织、知识产权管理制度、知识产权纠纷应对3个方面评估组织对知识产权管理的情况。知识产权管理组织关注是否有专业人员对产品的知识产权进行风险管理；知识产权管理制度评估是否制定了完善的知识产权管理规章制度，是组织进行知识产权管理的依据；知识产权纠纷应对通过审查组织在历史知识产权纠纷事件中的表现，了解组织在知识产权管理方面的实际能力。
（5）市场管理
市场管理主要从产品市场占有率、支持的指令集架构、应用软件生态3个方面评估产品的市场竞争力。市场占有率能直接反映软件的市场竞争力，支持的指令集架构和应用软件生态则是通过软件上下游的生态间接判断软件的市场竞争力。
C）软件知识产权评估维度
知识产权类指标主要从知识产权风险以及许可证限制风险两个方面评估软件产品在专利、著作权、许可证中存在的风险及限制。
（1）知识产权风险
知识产权风险是指在软件开发和使用过程中，可能面临的侵犯他人知识产权的法律风险。这类风险一旦发生，可能会导致软件无法继续使用或面临巨额赔偿。
1）代码侵权风险：需要对软件项目中的代码来源进行审核，确保没有侵犯他人的版权。特别是在引用第三方库或模块时，应当明确其授权协议是否允许当前的使用方式。
2）第三方贡献的合法性：对于开源社区中的外部贡献者提交的代码，需进行合法性审查，确保不会因引入第三方贡献而引发知识产权纠纷。
（2）许可证限制风险
开源软件通常会使用各种不同的许可证，这些许可证对于软件的使用、修改、发布等行为都有明确的限制。如果不仔细遵守这些限制，可能会引发法律纠纷，甚至导致软件的商业化运作受阻。
1）许可证兼容性：在使用多个开源软件或库时，需要评估它们的许可证之间是否兼容。例如，GPL许可证对软件的发布有较严格的要求，而Apache许可证则相对宽松，混合使用时需确保不违反许可证规定。
2）许可证合规性：确保在发布或商业化软件时，遵守所使用开源软件的许可证条款。例如，按GPL要求公开源代码，或根据MIT许可证注明原作者信息等。
3）许可证的延展性：评估软件许可证是否允许未来的商业化运作，以及是否对软件的扩展和变更有任何限制。例如，某些许可证可能限制了软件的二次分发或修改后的再发布。
D）软件可维护性评估维度
传统软件可维护性评价指标主要从易分析性、易修改性、模块性和易测试性等软件属性出发。本课题结合开源软件供应链规模化、社交化的发展特点，主要从以下几个指标进行评估：
（1）团队健康
团队健康性评估主要关注软件开发和维护团队的规模与协同能力。一个健康的团队通常由多个角色组成，具备良好的协作机制和较大的团队规模，这意味着在核心贡献者离职或流失的情况下，其他成员仍能够持续维护和更新软件，确保项目不会因人员变动而停滞不前。这种抗风险能力是软件长期稳定性和可持续性的关键指标。
（2）软件活跃度
软件活跃度评估侧重于软件在开发、维护和讨论等方面的动态表现，反映了其在特定时间段内的活跃水平。评估内容主要涵盖以下三个要素：
1）社区活跃度：社区活跃度是评估软件生存力与发展潜力的重要指标，反映了用户和开发者对软件的关注程度。活跃的社区意味着用户群体持续关注并积极贡献，这有助于推动软件的健康发展并提升其竞争力。相反，社区活跃度的下降可能预示着用户流失，进而影响软件的生命力。同时，社区活跃度也从侧面体现了软件的质量与影响力，只有高质量的软件才能吸引并留住更多用户和贡献者。
2）依赖平均更新时间：依赖库的更新频率是衡量软件维护性和安全性的重要指标。使用过时版本的依赖库会增加运维风险，而依赖库的频繁更新则表明软件在维护上较为积极，且具备较强的风险控制能力。因此，依赖的平均更新时间可以反映出软件在版本维护上的活跃度与安全性。
3）发布频率：发布频率是软件活跃度评估的关键因素之一。高频次的版本发布表明开发团队在持续改进软件并及时引入新功能和修复问题。每次新版本的发布都是软件发展进程中的一个重要里程碑，展示了项目的进展。相对较高的发布频率通常意味着开发团队对软件的积极维护与迭代，这对用户体验和软件的长期发展都具有重要意义。
（3）依赖影响力
依赖影响力是指在开源软件供应链中，有多少其他软件直接或间接依赖于当前软件。软件被广泛依赖通常意味着其功能稳定、质量可靠，已经通过了大量用户和场景的验证。广泛的依赖性不仅表明该软件满足了用户需求，也为其提供了更多的使用反馈，推动其不断改进和优化。因此，依赖影响力大的软件，往往具有较好的维护性和健全的风险应对机制，降低了可维护性风险的发生概率。
（4）外部依赖度
与依赖影响力相反，外部依赖度指的是一个软件对外部模块的依赖程度。通过复用外部代码，依赖项中的所有漏洞和缺陷都会通过依赖关系传递到软件自身，因为软件完全依赖这些外部模块。如果一个软件没有任何外部依赖项，那么其外部依赖度为0。这样的软件在维护上相对简单，因为维护人员只需专注于程序自身逻辑的实现，而不必担心外部依赖项可能带来的安全风险或技术债务。
（5）软件生态
软件生态是一个复杂的社会-技术系统，其环境可以是软件公司、研究组织，或者虚拟的开源或开放开发社区。开源软件基金通常通过多种方式支持开源软件的发展，如推广和宣传、提供开源项目指导、以及为软件开发和相关活动提供资金补助等。商业支持同样为软件的可持续发展提供了稳定的资金来源，保证了各种开发和维护活动的正常进行。
E）闭源二进制代码风险评估维度
异构复杂软件供应链中包含大量的闭源二进制代码，如何对这些来源信息不充分以及源码信息不足的闭源组件进行有效评估是本课题研究的重要一环。基于课题一与课题三中提供的二进制代码特征提取与成分分析结果，本课题围绕闭源二进制代码风险评估维度进一步提出相应的评估指标，具体如下：
（1）组件安全性评估
利用安全分析工具扫描二进制代码中的已知漏洞或恶意代码签名。分析工具应能够识别常见的安全漏洞，如缓冲区溢出、未初始化的内存使用、以及不安全的函数调用。将闭源二进制代码特征与已知漏洞数据库（如CVE数据库）进行比对，量化代码中已知漏洞的数量和严重程度。结合漏洞数量和安全更新频率，使用加权算法计算综合安全风险评分。
（2）自研率评估
基于二进制代码特征相似性比对，识别外部代码的比例。计算自研代码占总代码的百分比。自研代码比例较低，意味着外部供应链组件的使用比例较高，可能增加软件的潜在风险。
（3）依赖性评估
评估闭源二进制组件对外部库和服务的依赖程度，分析其可能带来的连锁风险。使用成分分析工具扫描闭源二进制代码，识别出使用的第三方库和服务。分析依赖库的层级关系和调用频率，评估核心组件对外部依赖的敏感性。根据依赖库的历史漏洞、维护状态、许可证合规性等指标，量化依赖库的风险。
（4）动态行为评估
通过运行时监测评估闭源二进制代码的实际行为，检测潜在的动态风险，如未预料的网络连接、权限提升等。将闭源二进制组件在受控的沙箱环境中运行，监测其行为，包括系统调用、网络活动、文件访问等。将运行时行为与正常操作模式对比，识别异常行为，如尝试连接未知IP地址、未经授权的文件修改等。评估组件在运行时的CPU、内存、磁盘等资源的占用情况，识别异常的资源消耗模式。计算运行期间检测到的异常行为数量并基于资源使用率计算性能消耗评分，结合行为异常次数和资源消耗评分，量化动态行为的风险。
（5）性能与效率评估
评估闭源二进制组件在实际运行环境中的性能表现，识别低效或资源浪费的部分。通过标准化的性能基准测试工具，评估组件在CPU、内存、IO等方面的性能表现。在模拟的高负载环境下运行组件，观察其在极端条件下的表现，包括响应时间、吞吐量、稳定性等，分析组件的稳定性（如崩溃次数、响应延迟）并量化得分。
F）基于多维度指标的量化评估
为了实现软件供应链安全风险的量化分析，本课题采用层次分析法（Analytic Hierarchy Process，AHP）对多维度评估指标进行综合评估，以确保评估结果的科学性和可靠性。该方法能够处理复杂的层次问题，并为不同维度指标分配合理的权重，从而实现对供应链安全风险的精确量化。
层次分析法确定权重的主要步骤为：1）根据同级指标之间的相对重要性比值，构造判断矩阵；2）为确保权重分配合理，应对判断矩阵进行一致性检验，若不符合一致性条件，需调整判断矩阵；3）求出判断矩阵的最大特征根对应的特征向量，并进行归一化，得到本级各指标的相对权重。
本课题综合考虑不同来源的组件与不同维度之间的关联性，筛选合适的指标维度对组件进行特性化评估，并量化评估结果，以此衡量组件在特定维度下的供应链安全风险。
2.5.2.3 基于源代码漏洞可达性分析技术研究
传统的软件成分分析技术通常首先识别出项目中引入的组件，然后进一步检测这些组件中潜在的漏洞，并将此类数据作为检测结果。然而，这种技术存在以下缺陷：
1.无法判断组件是否被实际使用：开发人员可能引入了某个开源组件，但并未在代码中实际引用或调用它。
2.无法识别组件的利用路径：即使开发人员在项目中使用了某个开源组件的代码，如果该代码没有被直接调用，或系统中不存在调用路径，漏洞也无法被利用。
3.漏洞利用条件复杂：某些漏洞的触发需要依赖特定的条件，如特定版本的JDK、操作系统或外部配置等。
由于上述问题，检测到的组件漏洞并不一定具备被利用或触发的可能性。
A) 建设漏洞可达性规则模板
由于每个组件的漏洞多种多样，且组件的使用方式、漏洞的利用手段以及修复方法各不相同，本课题计划借助大语言模型，对组件修复代码、漏洞PoC/Exp以及漏洞原理进行深入分析。从中提取出与漏洞相关的关键代码调用点（Sink点函数）和可能触发漏洞的特定配置项，进而生成一系列精准的检测规则。这些规则将有助于识别漏洞的真实利用路径和潜在风险点。
如下图所示是漏洞可达性分析规则模型示例图

图 21 漏洞可达性分析规则模型
B) 漏洞可达性验证
为了解决传统漏洞检测中的不足，本课题将研究并采用漏洞可达性验证技术。通过对项目源代码进行抽象语法树（AST）分析和配置文件的解析，结合预先生成的漏洞可达性规则，验证是否存在漏洞的可达路径或配置缺陷。如果确认存在实际的调用路径或配置问题导致漏洞可达，则该漏洞具备被真实利用的可能性，需优先修复。通过这种方式，可以有效降低漏洞误报率，提高漏洞检测的准确性和修复的优先级。
2.5.2.4 二进制软件包多维度分析评估技术研究
信创软件融合开源软件的同时，也潜藏着开源安全漏洞风险，可能直接或间接引入开源软件，或在开源软件的基础上二次封装，导致开源安全的专业性与复杂性难以评估，开源软件引入的安全漏洞依赖关系、影响范围、破坏程度等信息不明确，将给信创软件及厂商带来无法预估的风险。
信创软件的交付形式通常以二进制格式的软件成品及其配套设施为主，且信创软件的运行环境具有多样化特征。因此，对二进制形式的软件成品进行软件成分分析和漏洞检测成为当前工作的重中之重。本课题旨在针对信创软件多样化的运行场景，满足二进制软件快速特征提取和高效检测的需求。通过结合多维度的检测技术，本课题将实现交叉验证，从而提升检测的准确性和全面性。
本课题将针对信创多样化的场景环境，满足二进制软件快速特征提取与快速检测的需求，利用多种不同维度的检测技术进行交叉验证的检测技术，研究一种二进制软件包多维度分析评估技术，从二进制函数、函数调用关系、二进制字符串三个维度，多维度分析二进制中的软件成分组成，并进行交叉验证，提高二进制检测的准确度。
以下是检测技术原理。用作成分检测的二进制文件称为“原始二进制文件”，检测出的组件称为“来源二进制组件”。
（1）基于深度学习的二进制函数相似度模型检测技术
尽管学术界和工业界在二进制函数相似性比较技术方面取得了显著进展，但当前的二进制函数级别检测技术仍然存在一些局限性，特别是在处理异构环境时，效果有待提升。函数级别的组成成分分析技术，并未有将此技术落地在软件供应链场景的具体应用。本项目将利用课题1中研究的“基于深度学习的二进制函数向量相似度模型技术”，实现这一技术的应用。
首先，建立一个保护海量函数特征向量的数据库。将二进制组件的函数通过模型嵌入，得到函数的向量特征，再将向量特征存入向量数据库，并建立函数向量特征与组件的对应关系。对海量向量通过聚类将向量空间划分为多个子空间（或称为“桶”），每个子空间包含一组相似向量。查询时，首先找到包含查询向量最可能相似向量的几个子空间，然后在这些子空间内部进行精确搜索；并对数据进行逻辑上的分片和分区，便于管理和分布式处理。
其次，通过模型获取原始二进制文件中的全部函数向量，利用缓存机制存储热门查询的结果或部分索引结构，以减少磁盘I/O和加速查询响应，结合标量过滤条件，进行预计算以优化查询流程，使用近似最近邻搜索算法 HNSW将原始二进制文件的函数与向量数据库中的函数进行搜索匹配。
最后，得到相似度最高的的多对相似函数，通过该深度学习模型匹配到的任意一对函数，我们称之为“相似函数对”。若在原始二进制文件与来源二进制组件的函数匹配结果中，存在大量或高比例的相似函数对，可以检测出原始二进制中包含该来源二进制组件；若数量过少过比例过低，则需与其他检测技术相结合。
（2）基于图匹配的函数调用关系检测技术
对于“相似函数对”中的相似函数所来自的组件，称为“来源二进制组件”。基于检测函数在原始二进制文件中的函数调用关系，可以构建“检测函数调用关系图”；同样，基于相似函数在来源二进制组件中的函数调用关系，可以构建“相似函数调用关系图”。

图 22 检测函数调用关系图
如上图所示，在检测函数调用关系图中的任意一个被调用的函数，都能够通过深度学习模型，在相似函数调用关系图中找到其对应的相似函数。即在以A1和A2为起点的两个调用关系图中，存在路径对应关系：如在检测函数调用关系图中有{A1 -> C1}（A1调用C1），那么在相似函数调用关系图中则存在对应路径{A2 -> C2}（A2调用C2），且{A1,A2}是相似函数对，同时{C1,C2}是相似函数对，这一对应关系我们称之为“相似调用路径”，对于存在一定数量相似调用路径的，可以检测出原始二进制中包含该来源二进制组件。
（3）基于二进制字符串分词特征的检测技术
首先，对原始二进制文件中的函数符号、导入导出符号、ASCII字符串、UNICODE字符等字符级数据进行分词，并建立索引数据库。每个字符串都经过预处理以提取词干，随后对分词结果建立词汇表和倒排索引。基于此，创建了字符串倒排索引数据库，并建立了来源组件与字符串、字符串与倒排索引之间的实体关系。如下图所示，展示了索引生成及权重设定的过程。

图 23 索引生成及权重设定过程
根据每一个分词的价值进行权重设定，如下是部分权重价值表：
表 4 部分权重价值表
类型	权重	示例
版本号/电话/人名/...	高	V1.3.8
Web协议/域名/邮箱/...	中	https://xxxxx.com
单词/数字/...	低	hello
通过在倒排索引数据库中搜索原始二进制文件中的每个字符串，并计算所有分词权重的总和，权重最高的字符串所对应的来源二进制组件即可被检测为原始二进制文件中包含的来源二进制组件。
2.5.3 构建异构复杂软件供应链分析及风险评估平台技术研究
面向解决异构复杂软件供应链数据源的实时监控、高效采集和快速提取，以及应对漏洞关联函数识别和代码修复优化场景需求，本课题围绕构建异构复杂软件供应链分析及风险评估平台、构建海量开源项目监控与源数据采集系统以及构建基于大语言模型识别开源组件漏洞缺陷提取系统，依托现有平台技术，增强软件成分分析平台能力以及软件风险评估与管控能力。具体的研究技术路线和技术方案如下图所示。


图 24 技术路线和技术方案
2.5.3.1构建海量开源项目监控与源数据采集系统
在构建面向信创复杂场景的代码特征基因库平台技术研究中，对海量的都源代码与二进制代码进行监控采集是目前急需完善的技术领域。本项目将针对来自多种数据源的海量源代码与二进制代码进行监控与采集。
如下图是海量开源项目监控与源数据采集系统架构图。

图 25 海量开源项目监控与源数据采集系统架构图
通过自动化监控技术，对开源组件、信创供应链软件、开源软件、开源代码库、闭源软件等软件项目与数据源进行持续监控。对每个数据源进行识别，明确需要采集的数据源；对数据源中的项目进行元数据收集，包括其项目信息、项目结构等，确定其监控范围；并通过对代码仓库的监控，实时获取最新的代码提交记录，实时校验二进制软件包哈希值是否一致，确定其是否发生增量更新；若发生增量更新，则触发自动化采集。
对存储库中不存在的代码进行全量采集。当触发增量更新时，对更新的代码或软件包进行增量采集，并通过对增量代码进行提取合并，经过数据清洗与校验后存入存储库。
2.5.3.2构建分布式海量代码特征基因提取系统

图 26 分布式海量代码特征基因提取系统的架构图
上图展示的是一个分布式海量代码特征基因提取系统的架构图，主要分为两大部分：
1.存储库模块
存储库：图中展示一个数据存储库，存储了大量代码资源。存储库可以包含各种代码，如源代码和二进制代码。
获取代码：从存储库中提取代码的步骤。获取代码后，进入下一步进行调度和执行。
分布式调度系统：负责对从存储库获取的代码进行分布式调度，确保系统能够处理大规模的代码，并通过执行模块将代码输入到后续的处理步骤。
2.基因提取模块
代码输入：获取的代码进入该模块，进行基因提取。
二进制代码判定：有一个判定步骤判断输入的代码是否为二进制代码。如果是二进制代码，则执行二进制反汇编，将其转化为汇编或中间语言代码，以便进一步处理。
代码预处理：对于非二进制代码，系统会对代码进行预处理，去除语言无关的特征，比如代码行数、注释比例、模块依赖关系等。此步骤有助于标准化代码，使其更易于进行特征提取。
代码特征快速提取技术：在预处理后的代码中，系统利用快速提取技术提取出代码中的重要特征，生成代码的片段基因。该步骤是整个系统的核心，负责生成特征数据。
特征索引：提取的代码基因被索引，以便后续的检索与查询。
分布式基因库：最后，所有提取出的代码片段基因存储在分布式的基因库中，确保高效的存储和后续的查询。
本系统通过分布式调度和特征提取技术，能够高效处理海量代码并提取代码片段的核心基因。
2.5.3.3构建基于大语言模型识别开源组件漏洞缺陷提取系统
通过对开源组件漏洞代码修复的前后变化学习，构建漏洞缺陷函数的大语言分析模型，能够自动获取组件漏洞的关联函数，可以为代码分析中的组件漏洞可达和漏洞的修复提供有效的支撑。

图 27 开源组件漏洞缺陷提取系统
(a)漏洞关联组件项目代码仓库设计实现。通过使用NVD、GitHub Security Advisories等API接口，定期自动化采集CVE漏洞数据，比对公开漏洞关系库（osv等），精准筛选与Java相关的漏洞。利用NLP（自然语言处理）模型，深入解析漏洞公告、修复提交记录和版本发布日志，智能化关联CVE与开源组件项目仓库。该方案可以有效减少人工干预，提升数据获取和关联的精度。
(b)提取并分析漏洞修复前后的代码差异。支持大语言模型的修复行为学习，利用git diff工具提取漏洞修复前后的代码变化，同时结合AST（抽象语法树）分析，捕捉代码层次结构、函数调用关系、变量变化等关键信息。结合控制流与数据流分析技术，全面了解修复动作的上下文。该方案不仅聚焦代码的直接差异，还通过语义分析理解修复的逻辑和意图。
(c)大模型学习及修复行为模型构建。对大量已修复的Java漏洞修复数据进行训练，构建具备代码语义理解能力的深度学习模型。模型将学习漏洞修复的常见模式和策略，形成一套可以识别新漏洞并预测修复行为的知识库。该方案将采用增量学习机制，定期更新模型，以确保模型能跟随最新漏洞修复行为不断提升识别能力。

2.5.3.4软件成分分析平台能力增强技术
为满足当前异构复杂的供应链分析需求，针对不同系统及不同类型的分析对象，在现有产品能力基础上，引入源码高阶克隆检测技术、二进制软件包多维度分析评估技术以及基于深度学习模型的二进制检测技术。同时，面对海量的供应链基因数据，运用大数据分析技术增强分析效能。智能化的数据聚合与模式识别提高了风险捕捉的准确性与全面性，从而显著增强平台在复杂环境中的响应能力与分析效能，优化供应链安全管理和风险识别。
(1)增加高阶克隆检测实现自研率分析、二进制检测能力
基于包管理的源码检测分析，能够有效解决在源代码项目中存在包管理器文件（如Maven的POM文件等）时，针对第三方组件依赖和许可证进行分析的问题。通过Hash技术对文件和代码片段进行分析，可以识别等值内容的成分，从而基本满足软件开发中引入的开源组件或开源代码带来的风险管理需求。然而，对于异构复杂供应链中所面对的软件，这些方法仍存在局限，难以进行有效分析。通过引入最新的研究成果，可以解决无法解压、误报漏报等一系列问题，构建一个能够支持异构复杂软件供应链分析需求的治理平台。

图 28 检测能力演进图

高效的模拟构建分析。通过模拟软件编译过程，能够在脱离原编译器环境的情况下高效分析第三方组件的引入。此方法不仅大幅提升了分析效率，还增强了处理异常问题的能力，减少了对特定构建环境的依赖。
语义化代码检出。通过高阶源码克隆检测技术，系统能够实现语义级别的片段比对，精准识别语义相同但结构不同的代码片段，并具备非标准开源许可的检测能力，提升了代码相似性分析的广度和深度。
二进制文件分析。通过二进制函数相似度匹配和字符串特征分词技术，系统能够对除源码外软件包进行有效分析，结合大数据模型和AI技术，确保检测结果的准确性和全面性。
(2)处理能力增强：利用基于大数据的海量代码基因分析对比技
基于大数据的海量代码基因分析，带来了海量特征数据的存储和检索问题，通过大数据基础来实现对海量数据存续检索对比能力。如图，通过大数据技术来实现分析对比。

图 29 分析比对流程图
多任务多线程分析技术。在大规模代码基因特征提取过程中，多任务多线程技术能够显著提高分析的效率。基因特征提取需要对大量的源码和二进制文件进行并行分析，以识别出特定的代码片段和函数的相似度。通过多任务多线程技术，可以将大数据集拆分成多个子任务，每个线程独立执行一部分数据的分析，从而充分利用系统的计算资源，缩短整体分析时间。具体实现流程包括：
任务分解：将需要处理的分析对象按照一定规则划分为多个子任务，比如按文件、代码块或函数级别进行拆分。多线程并行处理：通过线程池或并行框架，为每个子任务分配独立线程进行特征提取。每个线程对代码或二进制文件进行语法、语义或相似度特征的分析。
结果汇总：各线程完成子任务后，将提取到的基因特征数据汇总到主线程，进行统一的结果分析与优化，最终形成全局特征库或用于比对的基因图谱。
通过多任务多线程的并行处理，不仅提升了特征提取的速度，也增强了对海量数据的处理能力，有效应对大规模供应链代码基因的复杂分析需求。
大数据模型与存储。在海量基因数据的背景下，大数据存储和检索技术尤为重要。根据基因数据的不同类型和检索时的业务特性，采用专门的存储模型，来提高海量数据的检索能力：
二进制基因数据：采用向量存储引擎（如 Milvus）来处理大规模的矢量数据，以便于快速查找和比对。
结构化元数据：通过 MongoDB 存储代码的结构化信息，如组件依赖、版本、许可等。
代码片段基因：通过 Elasticsearch (ES) 实现全文检索，对海量的文本型基因片段进行快速匹配和查询。
HDFS 分布式文件系统：用于存储海量的原始数据、分析结果及历史记录，以支持大规模数据的持久化。
2.5.3.5软件风险评估与管控能力增强
(1)软件安全风险量化评估
基础的产品具备了组件路径溯源、组件漏洞识别风险治理，通过组件漏洞可达性分析和二进制软件包多维度分析将大大增强平台的安全风险评估能力。
组件漏洞可达性分析。软件供应链分析及风险评估采用软件成分分析工具进行开源组件及其漏洞分析，报告中大量的组件漏洞被检出，但是否可以被验证、可以被利用成为一大问题，通过漏洞可达性判断自研代码到漏洞是否存在可达路径，从而判断漏洞是否会被自研代码触发分析来检测潜在的漏洞和安全风险。

图 30 检出效果图

二进制软件包多维度分析评估。二进制文件分析其复杂底层代码结构，单一维度评估导致结果的高误报率、低置信度，通过多维度分析技术的引入，将分析维度覆盖到组件版本号匹配、函数向量匹配、字符串分词特征匹配、函数调用关系匹配、导入导出符号匹配，解决异构复杂（跨CPU架构、操作系统、编译器等）环境下无法精确对比二进制函数问题，并填补信创环境成分分析领域二进制函数级分析技术的空白。

(1)运行时漏洞靶向热修复技术
在静态源码组件风险的管控，已通过组件依赖修复和安全版本推荐实施了相应的风险处理方案。然而，对于已经上线的软件或者缺乏对应修复组件版本的情况，目前面临着重大挑战。此时，运行时组件漏洞靶向修复能够有效弥补这一技术空白，并且具备低侵入、精准修复的强大能力。部署示意图如下:

图 30 靶向修复部署示意图
对于已知的正在运行的 Java 应用组件漏洞 ，Java Agent 的 JAR 文件中含有在程序启动时或运行时被调用的 premain 方法或 agentmain 方法，在这些方法中加载插件，利用类加载器加载插件 JAR 文件并获取其中的类和方法，找打对应组件漏洞的sink点和需要修复的函数字节码位置，再通过反射机制调用修复函数并将其注入到正在运行的程序中。流程图如下：


图 32 靶向修复流程图
一旦插件被加载，Java Agent 能够在运行时拦截对漏洞 sink 点的方法调用，并将其重定向到修复函数，修复函数执行相应的修复操作以消除漏洞。
2.5.4 开展异构复杂软件供应链安全治理的示范应用
本课题通过示范应用验证异构复杂软件供应链安全治理方案的有效性和适用性，旨在建立一种适用于广泛行业的风险评估和安全治理框架。随着5G、物联网、云计算等新兴技术的快速发展，运营商行业不仅支撑着全国范围内的通信网络，还承载着海量的敏感数据和关键的通信服务，其中大量应用系统也逐步迁移至国产化开发运营环境中，运营商的网络架构和服务面临着前所未有的复杂性和安全挑战。在这一背景下，运营商行业能够覆盖大部分信创应用场景的验证需求，其涵盖的业务应用场景如软件自研、外包开发、现货采购和硬件设备采购具有广泛的代表性和复杂性。这些场景不仅涉及到网络设备、通信协议和数据处理等多个方面，还需要应对来自外部威胁和内部漏洞的双重挑战。
本课题计划在运营商行业进行重点应用示范，通过在运营商行业进行示范应用，可以有效检验和优化技术解决方案的综合能力，同时为其他行业提供有力的参考和借鉴，后基于成熟验证后的软件供应链安全治理体系，再覆盖至军工、金融、能源、政务、制造业、高校等关键信息行业。本课题的示范应用基于不同行业的共性及差异性，主要在以下四个场景中进行开展：
（1）软件自研开发场景
在软件自研开发场景中，各行业都需要在开发流程中嵌入高效的安全检测和风险评估机制，以确保软件在开发阶段能够识别和修复潜在的安全漏洞，从而保障最终产品的安全性和可靠性。为了实现这一目标，必须对现有平台进行优化和集成，以适应不同技术栈和开发需求。
首先，明确各行业对安全检测的具体要求。例如，金融行业对交易系统的高安全性需求，军工行业对数据安全的严格标准等。通过深入分析这些需求，制定出相应的安全检测标准和流程。其次，优化现有技术平台，并将其与开发环境进行集成。包括调整自动化安全检测工具的配置，以适应不同的编程语言、开发框架和应用场景。此外，针对特定行业的技术栈，优化平台的集成方式，实现工具和技术的无缝对接，从而提高整体开发效率和安全性。通过集成，确保软件供应链安全评估能力能够在软件开发的各个阶段进行有效的安全检测。最后，建立实时反馈机制，将安全检测结果及时传递给开发人员，并提供详细的修复建议。通过平台集成，实现检测结果自动化地流入开发环境，并结合开发工具提供即时反馈和修复建议。
通过这些措施，可以在软件开发过程中实现高效的安全管理，减少潜在的安全风险，提高系统的整体安全性和可靠性。同时，通过对平台的优化和集成，将提升开发流程的整体效率，确保开发人员能够快速响应和解决安全问题。
（2）软件外包开发场景
在软件外包开发场景中，各行业在验收外包软件时都必须进行详细的风险评估，以确保所接受的软件符合预定的安全标准，并准确评估其自研率。这一过程需要建立科学的风险评估模型，构建面向外包开发验收过程的软件供应链风险评估系统，并开发自动化工具来生成验收报告。首先，针对外包软件的特定需求，开发并优化风险评估模型，评估外包软件的安全性，识别可能存在的漏洞和风险。其次，设计自动化验收工具，简化外包软件的验收流程，自动生成验收报告，提供详细的风险评估结果。此外，实施自研率分析，准确区分外包组件和自研组件，评估其对整体软件安全的影响，确保外包软件的安全性和合规性，减少对供应商的依赖风险，提升软件的整体质量和安全性。
（3）现货软件采购场景
在现货软件采购场景中，各行业在采购现货软件时需要特别关注软件的漏洞检测和供应链风险管理，以确保所采购的软件在供应商断供的情况下仍能保持安全性。首先，基于研究的软件安全漏洞检测技术，针对现货软件中的潜在漏洞进行全面扫描和分析。例如，金融行业可能需要针对交易软件的安全性进行深入检测，而能源行业可能关注控制系统的软件稳定性。其次，基于供应链风险评估体系，评估现货软件在供应商断供或其他供应链中断情况下的风险，制定相应的应对策略。开发安全修复补丁管理系统，跟踪并验证补丁的应用情况，确保软件在长时间运行中能够保持高安全性。此外，生成详细的评估报告，为决策提供依据，确保软件的安全性和可持续性，帮助各行业有效管理现货软件的安全风险，保障业务的连续性和稳定性。
（4）硬件设备采购场景
在硬件设备采购场景中，各行业需要对采购的硬件设备进行内置固件的二进制分析和风险评估，以识别和解决潜在的安全漏洞。首先，基于研究二进制代码分析技术，对不同硬件设备的固件进行深入分析，识别潜在的安全漏洞和风险。其次，打造基于固件二进制的软件供应链风险检测系统，将固件二进制分析能力集成到现有的验收流程中，确保其能够在实际硬件环境中进行有效的安全评估。此外，进行实际硬件设备的集成测试，验证分析工具在不同硬件环境中的适应性和有效性。开发固件风险评估模型，结合固件分析结果，提供定制化的风险评估和缓解建议，确保硬件设备在实际使用中的安全性，帮助各行业确保硬件设备的固件安全，提升设备的整体可靠性和安全性。