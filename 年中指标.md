# 尖兵项目考核指标详细清单
## 截止到2025年12月31日指标要求

### 项目概述
本项目针对信创国产化场景下的异构复杂软件供应链风险评估与安全治理，重点研究面向源代码与二进制代码的软件成分分析技术，构建针对异构复杂软件供应链的风险评估体系。

---

## 一、数量指标

### 1.1 知识产权成果

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 是否里程碑成果 | 里程碑时间 | 考核方式 |
|---------|-----------|---------------|-----------------|---------------|-----------|----------|
| 技术发明专利（申请） | 0 | 5项 | 5项 | ✓ | 2025-12-31 | 发明专利受理证书 |
| 软件著作权 | 0 | 5项 | 11项 | ✗ | - | 软件著作权登记证书 |

### 1.2 学术成果

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 考核方式 |
|---------|-----------|---------------|-----------------|----------|
| 期刊论文 | 0 | 0 | 2项 | 发表论文 |

---

## 二、技术指标

### 2.1 核心技术指标

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 是否里程碑成果 | 里程碑时间 | 考核方式 |
|---------|-----------|---------------|-----------------|---------------|-----------|----------|
| 技术发明专利（授权） | 0 | 1项 | 6项 | ✓ | 2026-12-31 | 技术发明专利授权证书 |
| 覆盖编程语言种类 | 1种 | 2种 | 4种（C、C++、Java、JavaScript） | ✗ | - | 第三方测试报告 |

### 2.2 分析准确率指标

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 考核方式 |
|---------|-----------|---------------|-----------------|----------|
| 源代码级成分分析准确率 | 30% | 40% | 85% | 第三方测试报告 |
| 二进制代码级成分分析准确率 | 40% | 50% | 75% | 第三方测试报告 |
| 软件自研率分析准确度 | 50% | 60% | 90% | 第三方测试报告 |
| 漏洞检查风险分析准确度 | 50% | 60% | 90% | 第三方测试报告 |
| 许可证合规风险分析准确度 | 50% | 60% | 90% | 第三方测试报告 |

### 2.3 扩展技术能力指标

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 考核方式 |
|---------|-----------|---------------|-----------------|----------|
| 支持编程语言包管理器分析 | 1种 | 2种 | 10种（Go, C#, Ruby, Python, 鸿蒙仓颉等） | 第三方测试报告 |
| 支持二进制文件格式识别 | 10种 | 20种 | 50种（.exe、.msi、.bin、RAR等） | 第三方测试报告 |
| 支持处理器类型 | / | 3种 | 10种（龙芯、兆芯、海光、鲲鹏、飞腾、MIPS、SPARC、POWER、Intel、AMD等） | 第三方测试报告 |

### 2.4 数据库规模指标

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 考核方式 |
|---------|-----------|---------------|-----------------|----------|
| 二进制函数特征向量 | / | 1千万条 | 2千万条 | 第三方测试报告 |
| 二进制符号特征向量 | / | 1亿条 | 5亿条 | 第三方测试报告 |
| 二进制函数调用关系图 | / | 100万条 | 600万条 | 第三方测试报告 |

### 2.5 智能化分析能力指标

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 考核方式 |
|---------|-----------|---------------|-----------------|----------|
| 漏洞靶向修复补丁生成规则 | / | 100条 | 1000条 | 第三方测试报告 |
| Java应用软件热修复中间件支持 | / | 1种 | 3种国产信创中间件 | 第三方测试报告 |
| 源代码漏洞可达性分析CVE支持 | / | 100个CVE漏洞 | 500个CVE漏洞 | 第三方测试报告 |
| 软件供应链投毒检测情报数据 | / | 1千条 | 1万条 | 第三方测试报告 |
| SBOM标准格式支持 | / | 1种 | 3种 | 第三方测试报告 |

### 2.6 信创兼容性指标

| 指标名称 | 项目完成时指标值 | 考核方式 |
|---------|-----------------|----------|
| 国产信创操作系统兼容性认证 | 3种 | 第三方测试报告 |
| 国产信创数据库兼容性认证 | 1种 | 第三方测试报告 |
| 国产信创中间件兼容性认证 | 2种 | 第三方测试报告 |

---

## 三、质量指标

### 3.1 系统性能质量指标

| 质量维度 | 目标值 | 说明 |
|---------|--------|------|
| 分析准确率 | 85%（源代码）/ 75%（二进制） | 软件成分分析的核心质量指标 |
| 风险评估准确度 | 90% | 包括自研率、漏洞检查、许可证合规等 |
| 系统稳定性 | 支持大规模代码库分析 | 通过分布式架构保障 |
| 跨平台兼容性 | 支持10种处理器架构 | 覆盖信创和非信创环境 |

---

## 四、应用指标

### 4.1 行业应用示范

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 考核方式 |
|---------|-----------|---------------|-----------------|----------|
| 行业应用示范 | 0 | 1个应用示范 | 6个应用示范 | 用户使用报告 |

### 4.2 具体应用行业

1. **国防军工**：异构复杂软件供应链安全治理
2. **金融**：金融软件供应链风险管控
3. **运营商**：通信软件供应链安全评估
4. **能源**：能源行业软件安全治理
5. **政务**：政务系统软件供应链管控
6. **制造业**：制造业软件供应链风险评估

---

## 五、产业化指标

### 5.1 经济效益指标

| 指标名称 | 已有指标值 | 中期检查指标值 | 项目完成时指标值 | 考核方式 |
|---------|-----------|---------------|-----------------|----------|
| 相关销售收入 | 0 | 10万元 | 800万元 | 第三方审计报告 |

### 5.2 市场推广指标

| 指标名称 | 目标值 | 说明 |
|---------|--------|------|
| 产品技术就绪度 | TRL 9级 | 实现应用级别的技术成熟度 |
| 国产化替代能力 | 达到国内领跑水平 | 填补国产SCA函数级多维二进制分析领域空白 |

---

## 六、2025年关键里程碑成果

### 6.1 第一里程碑（2025年9月30日）
- **成果内容**：完成海量开源项目监控与源数据采集系统、分布式海量代码特征基因提取系统的原型研制
- **技术突破**：实现代码特征快速提取技术

### 6.2 第二里程碑（2025年12月31日）
- **成果内容**：完成异构复杂软件供应链分析及风险评估平台原型研发
- **约束性指标**：
  - 5个技术发明专利申请受理 ✓
  - 5个软件著作权 ✓

---

## 七、2025年各季度进度节点

### Q1 (2025年1-3月)
- 方案整体设计、技术调研
- 完成代码特征快速提取、软件包成分精准识别等关键技术研究及突破
- 完成面向源代码与二进制代码的软件成分分析技术研究

### Q2 (2025年4-6月)
- 完成软件风险量化评估等关键技术研究与突破
- 研发针对异构复杂软件供应链的风险评估体系
- 完成平台调研、系统设计报告和技术方案

### Q3 (2025年7-9月)
- 完成海量开源项目监控与源数据采集系统原型研制
- 完成分布式海量代码特征基因提取系统原型研制
- **实现第一个里程碑**

### Q4 (2025年10-12月)
- 完成异构复杂软件供应链分析及风险评估平台原型研发
- **实现第二个里程碑**
- 完成5个技术发明专利申请受理
- 完成5个软件著作权申请

---

## 八、考核方式说明

### 8.1 技术指标考核
- **第三方测试报告**：用于验证技术性能指标，包括准确率、支持能力等
- **功能验证**：通过实际测试验证系统功能完整性和性能指标

### 8.2 知识产权考核
- **专利受理证书**：证明专利申请的受理状态
- **专利授权证书**：证明专利的授权状态
- **软件著作权登记证书**：证明软件著作权的登记状态

### 8.3 应用效果考核
- **用户使用报告**：验证在各行业的实际应用效果
- **第三方审计报告**：验证产业化收入等经济指标

### 8.4 学术成果考核
- **发表论文**：通过期刊发表验证理论研究成果

---

## 九、重点关注事项

### 9.1 2025年必须完成的核心指标
1. **5个技术发明专利申请受理**（里程碑成果）
2. **5个软件著作权**
3. **源代码分析准确率达到40%**（中期目标）
4. **二进制代码分析准确率达到50%**（中期目标）
5. **完成1个行业应用示范**

### 9.2 技术突破重点
1. 填补国产SCA函数级多维二进制分析领域空白
2. 突破二进制分析低检出率、高误报率问题
3. 解决大规模代码库中特征提取效率低的瓶颈
4. 实现基于大语言模型的漏洞缺陷自动提取

### 9.3 风险控制要点
- 确保各项技术指标按时达成
- 重点关注里程碑成果的时间节点
- 加强与应用示范单位的合作
- 及时申请知识产权保护

---

*本文档基于《尖兵项目指标.txt》整理，涵盖截止到2025年12月31日的所有考核指标要求。*
