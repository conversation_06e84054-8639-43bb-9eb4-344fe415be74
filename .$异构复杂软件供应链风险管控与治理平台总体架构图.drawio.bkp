<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15">
  <diagram name="系统总体架构" id="system-architecture">
    <mxGraphModel dx="2066" dy="1219" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="异构复杂软件供应链风险管控与治理平台总体架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="284" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        <mxCell id="app-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="80" y="80" width="1000" height="70" as="geometry" />
        </mxCell>
        <mxCell id="web-portal" value="Web管理门户" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=12;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="100" y="95" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="api-gateway" value="API网关" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=12;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="240" y="95" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cicd-integration" value="CI/CD集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=12;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="380" y="95" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="report-system" value="报告系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=12;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="520" y="95" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="alert-system" value="预警系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=12;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="660" y="95" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="industry-apps" value="行业应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=12;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="800" y="95" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="mobile-app" value="移动应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=12;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="940" y="95" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="risk-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#2E7D32;" parent="1" vertex="1">
          <mxGeometry x="80" y="210" width="1000" height="150" as="geometry" />
        </mxCell>
        <mxCell id="multi-risk-eval" value="多维度风险评估系统&#xa;• 技术维度评估&#xa;• 管理维度评估&#xa;• 知识产权评估&#xa;• 可维护性评估&#xa;• 闭源组件评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=11;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="125" y="225" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="threat-modeling" value="威胁建模系统&#xa;• 威胁树模型&#xa;• FAHP权重计算&#xa;• 量化风险评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=11;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="310" y="225" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="vuln-reachability" value="漏洞可达性分析&#xa;• AST分析&#xa;• 配置文件解析&#xa;• 1582个CVE支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=11;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="495" y="225" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="supply-chain-monitor" value="供应链监控&#xa;• 投毒检测(3万条)&#xa;• 实时预警&#xa;• 风险追踪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=11;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="680" y="225" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="repair-system" value="靶向修复系统&#xa;• 1582个修复规则&#xa;• 自动化修复建议&#xa;• 热修复支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=11;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="865" y="225" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="analysis-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#E65100;" parent="1" vertex="1">
          <mxGeometry x="80" y="405" width="1000" height="150" as="geometry" />
        </mxCell>
        <mxCell id="source-analysis" value="源代码分析引擎&#xa;• 跨语言克隆检测&#xa;• 自监督学习&#xa;• 4种语言支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=11;fontColor=#E65100;align=left;" parent="1" vertex="1">
          <mxGeometry x="122" y="420" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="binary-analysis" value="二进制分析引擎&#xa;• 深度学习检测&#xa;• 5种处理器支持&#xa;• 27种文件格式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=11;fontColor=#E65100;align=left;" parent="1" vertex="1">
          <mxGeometry x="307" y="425" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="llm-analysis" value="大语言模型引擎&#xa;• 漏洞缺陷提取&#xa;• 修复行为学习&#xa;• 规则自动生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=11;fontColor=#E65100;align=left;" parent="1" vertex="1">
          <mxGeometry x="492" y="425" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="similarity-engine" value="相似性检测引擎&#xa;• HNSW算法&#xa;• 毫秒级响应&#xa;• 亿级向量检索" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=11;fontColor=#E65100;align=left;" parent="1" vertex="1">
          <mxGeometry x="677" y="425" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="unpack-engine" value="解包分析引擎&#xa;• 启发式解包&#xa;• 字节特征识别&#xa;• 多重封装支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=11;fontColor=#E65100;align=left;" parent="1" vertex="1">
          <mxGeometry x="862" y="425" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="feature-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#9C27B0;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#6A1B9A;" parent="1" vertex="1">
          <mxGeometry x="80" y="645" width="1000" height="150" as="geometry" />
        </mxCell>
        <mxCell id="incremental-gene" value="增量基因提取&#xa;• 代码片段标记&#xa;• 90%效率提升&#xa;• 千万行级支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=left;" parent="1" vertex="1">
          <mxGeometry x="122" y="660" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="binary-feature" value="二进制特征提取&#xa;• CNN+Triplet网络&#xa;• 函数向量化&#xa;• 跨架构适应" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=left;" parent="1" vertex="1">
          <mxGeometry x="307" y="660" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="semantic-feature" value="语义特征提取&#xa;• AST解析&#xa;• 控制流分析&#xa;• 数据流分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=left;" parent="1" vertex="1">
          <mxGeometry x="499" y="660" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="module-feature" value="模块化特征提取&#xa;• 程序模块化&#xa;• 语法语义融合&#xa;• 第三方库检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=left;" parent="1" vertex="1">
          <mxGeometry x="680" y="660" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="distributed-extract" value="分布式提取系统&#xa;• Kubernetes集群&#xa;• 并行处理&#xa;• PB级存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=left;" parent="1" vertex="1">
          <mxGeometry x="865" y="660" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="data-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#C62828;" parent="1" vertex="1">
          <mxGeometry x="80" y="890" width="1000" height="150" as="geometry" />
        </mxCell>
        <mxCell id="github-monitor" value="GitHub监控&#xa;• 实时代码监控&#xa;• 增量更新采集&#xa;• TB级日处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=left;" parent="1" vertex="1">
          <mxGeometry x="122" y="905" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="package-monitor" value="包管理器监控&#xa;• NPM/Maven/PyPI&#xa;• 3种包管理器&#xa;• 依赖关系分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=left;" parent="1" vertex="1">
          <mxGeometry x="495" y="905" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="vuln-db" value="漏洞数据库&#xa;• CVE数据采集&#xa;• 安全公告监控&#xa;• 威胁情报收集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=left;" parent="1" vertex="1">
          <mxGeometry x="680" y="905" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="binary-collect" value="二进制文件采集&#xa;• 固件文件&#xa;• 可执行文件&#xa;• 库文件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=left;" parent="1" vertex="1">
          <mxGeometry x="865" y="905" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="license-db" value="许可证数据库&#xa;• 开源许可证&#xa;• 兼容性规则&#xa;• 合规检查" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=left;" parent="1" vertex="1">
          <mxGeometry x="310" y="905" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="storage-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E0F2F1;strokeColor=#00796B;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#004D40;" parent="1" vertex="1">
          <mxGeometry x="80" y="1115" width="1000" height="150" as="geometry" />
        </mxCell>
        <mxCell id="vector-db" value="向量数据库&#xa;• 1200万函数向量&#xa;• 2亿符号向量&#xa;• 224万调用关系" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=11;fontColor=#004D40;align=left;" parent="1" vertex="1">
          <mxGeometry x="122" y="1130" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="gene-db" value="基因特征库&#xa;• 代码片段基因&#xa;• 增量更新&#xa;• 高效检索" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=11;fontColor=#004D40;align=left;" parent="1" vertex="1">
          <mxGeometry x="310" y="1130" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="rule-db" value="规则知识库&#xa;• 1582个修复规则&#xa;• 漏洞模式库&#xa;• 威胁情报库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=11;fontColor=#004D40;align=left;" parent="1" vertex="1">
          <mxGeometry x="495" y="1130" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="metadata-db" value="元数据存储&#xa;• 项目信息&#xa;• 依赖关系&#xa;• 版本历史" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=11;fontColor=#004D40;align=left;" parent="1" vertex="1">
          <mxGeometry x="680" y="1126" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="distributed-storage" value="分布式存储&#xa;• HDFS集群&#xa;• PB级容量&#xa;• 高可用性" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=11;fontColor=#004D40;align=left;" parent="1" vertex="1">
          <mxGeometry x="865" y="1126" width="170" height="120" as="geometry" />
        </mxCell>
        <mxCell id="conn1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#1976D2;strokeWidth=2;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" target="risk-layer" edge="1" source="app-layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="577" y="230" as="sourcePoint" />
            <mxPoint x="577" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="risk-layer" target="analysis-layer" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="585" y="360" as="sourcePoint" />
            <mxPoint x="585" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" edge="1" target="feature-layer" source="analysis-layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1124" y="720" as="sourcePoint" />
            <mxPoint x="1120" y="940" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#9C27B0;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1" source="feature-layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="577" y="1190" as="sourcePoint" />
            <mxPoint x="580" y="890" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn5" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#F44336;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" edge="1" target="storage-layer" source="data-layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1110" y="1340" as="sourcePoint" />
            <mxPoint x="1110" y="1370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edKtNmbJ0BeGJy5zsXmE-1" value="&lt;span style=&quot;color: rgb(46, 125, 50); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 700; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;风险评估层&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="85" y="180" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edKtNmbJ0BeGJy5zsXmE-2" value="&lt;span style=&quot;color: rgb(230, 81, 0); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 700; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;分析引擎层&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="85" y="380" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="T_0stK5keBRt5VaZhNy4-1" value="&lt;span style=&quot;color: rgb(21, 101, 192); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 700; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;应用服务层&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="50" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="T_0stK5keBRt5VaZhNy4-2" value="&lt;span style=&quot;color: rgb(106, 27, 154); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 700; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;特征提取层&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="85" y="617" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="T_0stK5keBRt5VaZhNy4-3" value="&lt;span style=&quot;color: rgb(198, 40, 40); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 700; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;数据采集层&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="85" y="860" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="T_0stK5keBRt5VaZhNy4-4" value="&lt;span style=&quot;color: rgb(0, 77, 64); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 700; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;数据存储层&lt;/span&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="85" y="1086" width="100" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
