<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15">
  <diagram name="增量基因标记流程" id="incremental-gene">
    <mxGraphModel dx="1018" dy="701" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="增量代码片段基因标记技术流程图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="284" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        <mxCell id="input-stage" value="输入阶段" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="80" y="80" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="code-repo" value="代码仓库&#xa;• GitHub/GitLab&#xa;• 版本控制&#xa;• 提交历史" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=left;" parent="1" vertex="1">
          <mxGeometry x="100" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="commit-diff" value="提交差异检测&#xa;• Git diff分析&#xa;• 变更文件识别&#xa;• 增量代码定位" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=left;" parent="1" vertex="1">
          <mxGeometry x="100" y="220" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="step1-title" value="步骤1：代码基因标记" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#2E7D32;" parent="1" vertex="1">
          <mxGeometry x="320" y="80" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="incremental-code" value="增量代码识别&#xa;• 新增代码行&#xa;• 修改代码行&#xa;• 删除代码行" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=11;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="340" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="adjacent-code" value="相邻代码标记&#xa;• 前后N行代码&#xa;• 上下文信息&#xa;• 语义边界" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=11;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="340" y="220" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="gene-marking" value="基因标记生成&#xa;• 哈希值计算&#xa;• 唯一标识符&#xa;• 位置信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=11;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="340" y="300" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="step2-title" value="步骤2：数据记录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#E65100;" parent="1" vertex="1">
          <mxGeometry x="560" y="80" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="gene-sequence" value="基因序列记录&#xa;• 文件内基因列表&#xa;• 序列顺序&#xa;• 依赖关系" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=11;fontColor=#E65100;align=left;" parent="1" vertex="1">
          <mxGeometry x="580" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="change-metrics" value="变更量统计&#xa;• 变更行数&#xa;• 变更比例&#xa;• 影响范围" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=11;fontColor=#E65100;align=left;" parent="1" vertex="1">
          <mxGeometry x="580" y="220" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="metadata-store" value="元数据存储&#xa;• 时间戳&#xa;• 作者信息&#xa;• 提交信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=11;fontColor=#E65100;align=left;" parent="1" vertex="1">
          <mxGeometry x="580" y="300" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="step3-title" value="步骤3：增量计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#9C27B0;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#6A1B9A;" parent="1" vertex="1">
          <mxGeometry x="800" y="80" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="incremental-hash" value="增量哈希计算&#xa;• 仅计算变更部分&#xa;• 快速哈希算法&#xa;• 内容指纹" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=left;" parent="1" vertex="1">
          <mxGeometry x="820" y="140" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="feature-extract" value="特征提取优化&#xa;• 跳过未变更部分&#xa;• 缓存复用&#xa;• 差异化处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=left;" parent="1" vertex="1">
          <mxGeometry x="820" y="220" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="storage-optimize" value="存储优化&#xa;• 增量存储&#xa;• 压缩算法&#xa;• 去重处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=left;" parent="1" vertex="1">
          <mxGeometry x="820" y="300" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="step4-title" value="步骤4：基因对比" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#C62828;" parent="1" vertex="1">
          <mxGeometry x="320" y="400" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="threshold-check" value="阈值判断&#xa;• 变更比例计算&#xa;• 阈值比较&#xa;• 策略选择" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=left;" parent="1" vertex="1">
          <mxGeometry x="200" y="460" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fast-compare" value="快速对比模式&#xa;• 基因直接比较&#xa;• 高效匹配&#xa;• 结果输出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=left;" parent="1" vertex="1">
          <mxGeometry x="380" y="460" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="full-compare" value="全量对比模式&#xa;• 代码还原&#xa;• 完整比较&#xa;• 精确匹配" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=left;" parent="1" vertex="1">
          <mxGeometry x="560" y="460" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="output-title" value="输出结果" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E0F2F1;strokeColor=#00796B;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#004D40;" parent="1" vertex="1">
          <mxGeometry x="320" y="560" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="similarity-result" value="相似性结果&#xa;• 匹配度评分&#xa;• 相似代码片段&#xa;• 差异分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=11;fontColor=#004D40;align=left;" parent="1" vertex="1">
          <mxGeometry x="200" y="620" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="performance-metrics" value="性能指标&#xa;• 处理时间&#xa;• 内存使用&#xa;• 效率提升" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=11;fontColor=#004D40;align=left;" parent="1" vertex="1">
          <mxGeometry x="380" y="620" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="optimization-report" value="优化报告&#xa;• 缓存命中率&#xa;• 存储节省&#xa;• 处理加速" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=11;fontColor=#004D40;align=left;" parent="1" vertex="1">
          <mxGeometry x="560" y="620" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="conn1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#1976D2;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="170" as="sourcePoint" />
            <mxPoint x="340" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="170" as="sourcePoint" />
            <mxPoint x="580" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="740" y="170" as="sourcePoint" />
            <mxPoint x="820" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#9C27B0;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="360" as="sourcePoint" />
            <mxPoint x="420" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn5" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#F44336;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="520" as="sourcePoint" />
            <mxPoint x="420" y="560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="decision-diamond" value="变更比例&#xa;&gt; 阈值?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#FFF9C4;strokeColor=#F57F17;fontSize=11;fontColor=#F57F17;" parent="1" vertex="1">
          <mxGeometry x="360" y="540" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="yes-branch" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#F57F17;" parent="1" vertex="1">
          <mxGeometry x="500" y="540" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="no-branch" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#F57F17;" parent="1" vertex="1">
          <mxGeometry x="300" y="540" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="performance" value="性能指标&#xa;• 处理速度: 10万行/秒&#xa;• 内存占用: 降低70%&#xa;• 存储空间: 节省80%&#xa;• 缓存命中率: 95%+&#xa;• 准确率: 99%+&#xa;• 支持并发处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#9E9E9E;fontSize=11;fontColor=#424242;align=left;" parent="1" vertex="1">
          <mxGeometry x="780" y="460" width="200" height="120" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
