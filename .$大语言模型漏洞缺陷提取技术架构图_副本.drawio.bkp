<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-28T10:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="大语言模型漏洞提取" id="llm-vulnerability">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="基于大语言模型的漏洞缺陷提取技术架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#1565C0;" vertex="1" parent="1">
          <mxGeometry x="284" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        
        <!-- 数据输入层 -->
        <mxCell id="input-layer" value="数据输入层 - 多源代码仓库监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#1565C0;" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="1000" height="40" as="geometry" />
        </mxCell>
        
        <!-- 数据源 -->
        <mxCell id="github-monitor" value="GitHub监控&#xa;• 实时提交监控&#xa;• Webhook事件&#xa;• API轮询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=left;" vertex="1" parent="1">
          <mxGeometry x="100" y="140" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="gitlab-monitor" value="GitLab监控&#xa;• 企业仓库&#xa;• 私有部署&#xa;• 安全访问" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=left;" vertex="1" parent="1">
          <mxGeometry x="260" y="140" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="commit-analysis" value="提交记录分析&#xa;• Commit信息&#xa;• 代码差异&#xa;• 变更历史" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=left;" vertex="1" parent="1">
          <mxGeometry x="420" y="140" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="issue-tracker" value="问题跟踪&#xa;• Issue关联&#xa;• Bug报告&#xa;• 修复记录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=left;" vertex="1" parent="1">
          <mxGeometry x="580" y="140" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="security-advisory" value="安全公告&#xa;• CVE数据库&#xa;• 安全补丁&#xa;• 漏洞披露" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=left;" vertex="1" parent="1">
          <mxGeometry x="740" y="140" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="code-review" value="代码审查&#xa;• Pull Request&#xa;• 审查评论&#xa;• 修改建议" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=left;" vertex="1" parent="1">
          <mxGeometry x="900" y="140" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 组件1：缺陷修复行为识别 -->
        <mxCell id="component1-title" value="组件1：缺陷修复行为识别" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="80" y="240" width="480" height="40" as="geometry" />
        </mxCell>
        
        <!-- 大语言模型处理 -->
        <mxCell id="llm-preprocessing" value="文本预处理&#xa;• 提交信息清洗&#xa;• 代码格式化&#xa;• 噪声过滤" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" vertex="1" parent="1">
          <mxGeometry x="100" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="llm-model" value="大语言模型&#xa;• GPT/BERT架构&#xa;• 代码理解&#xa;• 语义分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" vertex="1" parent="1">
          <mxGeometry x="240" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="pattern-recognition" value="模式识别&#xa;• 修复模式&#xa;• 关键词匹配&#xa;• 上下文理解" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" vertex="1" parent="1">
          <mxGeometry x="380" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="behavior-classification" value="行为分类&#xa;• 漏洞修复&#xa;• 功能改进&#xa;• 性能优化&#xa;• 代码重构" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" vertex="1" parent="1">
          <mxGeometry x="100" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="confidence-scoring" value="置信度评分&#xa;• 概率计算&#xa;• 阈值判断&#xa;• 结果筛选" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" vertex="1" parent="1">
          <mxGeometry x="240" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="defect-function-id" value="缺陷函数定位&#xa;• 函数级定位&#xa;• 代码行标记&#xa;• 影响范围" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" vertex="1" parent="1">
          <mxGeometry x="380" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 组件2：关联函数提取 -->
        <mxCell id="component2-title" value="组件2：关联函数提取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#E65100;" vertex="1" parent="1">
          <mxGeometry x="600" y="240" width="480" height="40" as="geometry" />
        </mxCell>
        
        <!-- AST分析 -->
        <mxCell id="ast-parsing" value="AST解析&#xa;• 语法树构建&#xa;• 节点分析&#xa;• 结构理解" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=left;" vertex="1" parent="1">
          <mxGeometry x="620" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="call-graph" value="调用图构建&#xa;• 函数调用关系&#xa;• 依赖分析&#xa;• 调用链追踪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=left;" vertex="1" parent="1">
          <mxGeometry x="760" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-flow" value="数据流分析&#xa;• 变量传播&#xa;• 数据依赖&#xa;• 影响传播" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=left;" vertex="1" parent="1">
          <mxGeometry x="900" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="caller-analysis" value="调用者分析&#xa;• 上游函数&#xa;• 调用路径&#xa;• 影响评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=left;" vertex="1" parent="1">
          <mxGeometry x="620" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="callee-analysis" value="被调用者分析&#xa;• 下游函数&#xa;• 依赖函数&#xa;• 传播路径" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=left;" vertex="1" parent="1">
          <mxGeometry x="760" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="related-functions" value="关联函数列表&#xa;• 直接关联&#xa;• 间接关联&#xa;• 风险等级" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=left;" vertex="1" parent="1">
          <mxGeometry x="900" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 组件3：规则生成 -->
        <mxCell id="component3-title" value="组件3：规则自动生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#9C27B0;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#6A1B9A;" vertex="1" parent="1">
          <mxGeometry x="80" y="480" width="480" height="40" as="geometry" />
        </mxCell>
        
        <!-- 规则生成模块 -->
        <mxCell id="vulnerability-pattern" value="漏洞模式提取&#xa;• 代码模式&#xa;• 漏洞特征&#xa;• 修复模式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=10;fontColor=#6A1B9A;align=left;" vertex="1" parent="1">
          <mxGeometry x="100" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="rule-template" value="规则模板生成&#xa;• 检测规则&#xa;• 匹配条件&#xa;• 触发条件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=10;fontColor=#6A1B9A;align=left;" vertex="1" parent="1">
          <mxGeometry x="240" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="repair-suggestion" value="修复建议生成&#xa;• 修复方案&#xa;• 代码示例&#xa;• 最佳实践" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=10;fontColor=#6A1B9A;align=left;" vertex="1" parent="1">
          <mxGeometry x="380" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 组件4：知识库管理 -->
        <mxCell id="component4-title" value="组件4：知识库管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="600" y="480" width="480" height="40" as="geometry" />
        </mxCell>
        
        <!-- 知识库模块 -->
        <mxCell id="rule-database" value="规则知识库&#xa;• 1582个规则&#xa;• 版本管理&#xa;• 质量评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=10;fontColor=#C62828;align=left;" vertex="1" parent="1">
          <mxGeometry x="620" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="vulnerability-kb" value="漏洞知识库&#xa;• CVE映射&#xa;• 漏洞分类&#xa;• 影响评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=10;fontColor=#C62828;align=left;" vertex="1" parent="1">
          <mxGeometry x="760" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="learning-feedback" value="学习反馈&#xa;• 效果评估&#xa;• 模型优化&#xa;• 持续学习" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=10;fontColor=#C62828;align=left;" vertex="1" parent="1">
          <mxGeometry x="900" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 输出层 -->
        <mxCell id="output-layer" value="输出层 - 智能化漏洞检测与修复" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E0F2F1;strokeColor=#00796B;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#004D40;" vertex="1" parent="1">
          <mxGeometry x="80" y="640" width="1000" height="40" as="geometry" />
        </mxCell>
        
        <!-- 输出结果 -->
        <mxCell id="reachability-rules" value="可达性分析规则&#xa;• 1582个CVE规则&#xa;• 路径分析&#xa;• 风险评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=10;fontColor=#004D40;align=left;" vertex="1" parent="1">
          <mxGeometry x="120" y="700" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="repair-patches" value="靶向修复补丁&#xa;• 自动化修复&#xa;• 代码生成&#xa;• 测试验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=10;fontColor=#004D40;align=left;" vertex="1" parent="1">
          <mxGeometry x="280" y="700" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="vulnerability-report" value="漏洞分析报告&#xa;• 详细分析&#xa;• 影响评估&#xa;• 修复建议" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=10;fontColor=#004D40;align=left;" vertex="1" parent="1">
          <mxGeometry x="440" y="700" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="risk-assessment" value="风险评估结果&#xa;• 风险等级&#xa;• 优先级排序&#xa;• 处理建议" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=10;fontColor=#004D40;align=left;" vertex="1" parent="1">
          <mxGeometry x="600" y="700" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="integration-api" value="集成API接口&#xa;• RESTful API&#xa;• SDK支持&#xa;• 第三方集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=10;fontColor=#004D40;align=left;" vertex="1" parent="1">
          <mxGeometry x="760" y="700" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="monitoring-alerts" value="监控预警&#xa;• 实时监控&#xa;• 异常告警&#xa;• 趋势分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B2DFDB;strokeColor=#00796B;fontSize=10;fontColor=#004D40;align=left;" vertex="1" parent="1">
          <mxGeometry x="920" y="700" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="conn1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#1976D2;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="200" as="sourcePoint" />
            <mxPoint x="320" y="240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#1976D2;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="840" y="200" as="sourcePoint" />
            <mxPoint x="840" y="240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="330" as="sourcePoint" />
            <mxPoint x="620" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="440" as="sourcePoint" />
            <mxPoint x="320" y="480" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn5" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="840" y="440" as="sourcePoint" />
            <mxPoint x="840" y="480" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn6" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#9C27B0;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="580" as="sourcePoint" />
            <mxPoint x="620" y="580" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="conn7" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#F44336;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="600" as="sourcePoint" />
            <mxPoint x="580" y="640" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 技术指标 -->
        <mxCell id="tech-metrics" value="技术指标&#xa;• 1582个修复规则&#xa;• 1582个CVE支持&#xa;• 95%+识别准确率&#xa;• 秒级响应时间&#xa;• 支持多种语言&#xa;• 自动化程度90%+" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#9E9E9E;fontSize=11;fontColor=#424242;align=left;" vertex="1" parent="1">
          <mxGeometry x="80" y="780" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 创新特色 -->
        <mxCell id="innovations" value="创新特色&#xa;• 大语言模型驱动&#xa;• 自动规则生成&#xa;• 智能模式识别&#xa;• 多维度分析&#xa;• 持续学习优化&#xa;• 端到端自动化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#9E9E9E;fontSize=11;fontColor=#424242;align=left;" vertex="1" parent="1">
          <mxGeometry x="300" y="780" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 应用价值 -->
        <mxCell id="application-value" value="应用价值&#xa;• 降低人工成本&#xa;• 提高检测效率&#xa;• 减少漏报误报&#xa;• 加速修复过程&#xa;• 知识积累沉淀&#xa;• 持续能力提升" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#9E9E9E;fontSize=11;fontColor=#424242;align=left;" vertex="1" parent="1">
          <mxGeometry x="520" y="780" width="200" height="120" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
