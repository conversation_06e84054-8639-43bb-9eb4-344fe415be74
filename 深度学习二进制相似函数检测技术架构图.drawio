<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15">
  <diagram name="深度学习二进制检测" id="binary-detection">
    <mxGraphModel dx="1018" dy="701" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="基于深度学习的异构二进制相似函数检测技术架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="284" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        <mxCell id="input-section" value="输入层 - 异构二进制函数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#1565C0;" parent="1" vertex="1">
          <mxGeometry x="80" y="80" width="1000" height="40" as="geometry" />
        </mxCell>
        <mxCell id="x86-func" value="x86架构函数&#xa;Intel/AMD" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=center;" parent="1" vertex="1">
          <mxGeometry x="100" y="140" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="arm-func" value="ARM架构函数&#xa;鲲鹏/飞腾" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=center;" parent="1" vertex="1">
          <mxGeometry x="240" y="140" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="mips-func" value="MIPS架构函数&#xa;龙芯" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=center;" parent="1" vertex="1">
          <mxGeometry x="380" y="140" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="hygon-func" value="海光架构函数&#xa;海光DCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=center;" parent="1" vertex="1">
          <mxGeometry x="520" y="140" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="other-func" value="其他架构函数&#xa;兆芯等" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;fontSize=11;fontColor=#1565C0;align=center;" parent="1" vertex="1">
          <mxGeometry x="660" y="140" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="preprocess-section" value="预处理层 - 多维度特征提取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#2E7D32;" parent="1" vertex="1">
          <mxGeometry x="80" y="220" width="1000" height="40" as="geometry" />
        </mxCell>
        <mxCell id="basic-block" value="基本块特征提取&#xa;• 指令序列&#xa;• 操作码统计&#xa;• 寄存器使用&#xa;• 常量分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="100" y="280" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="cfg-feature" value="控制流图特征&#xa;• CFG结构&#xa;• 分支模式&#xa;• 循环检测&#xa;• 路径分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="260" y="280" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="dataflow-feature" value="数据流特征&#xa;• 变量依赖&#xa;• 数据传播&#xa;• 内存访问&#xa;• 函数调用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="420" y="280" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="semantic-feature" value="语义特征&#xa;• 函数语义&#xa;• API调用&#xa;• 字符串常量&#xa;• 算法模式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="580" y="280" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="structural-feature" value="结构特征&#xa;• 函数大小&#xa;• 复杂度&#xa;• 嵌套深度&#xa;• 调用关系" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#2E7D32;align=left;" parent="1" vertex="1">
          <mxGeometry x="740" y="280" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="cnn-section" value="CNN网络层 - 多层卷积神经网络" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#E65100;" parent="1" vertex="1">
          <mxGeometry x="80" y="380" width="1000" height="40" as="geometry" />
        </mxCell>
        <mxCell id="conv1" value="卷积层1&#xa;• 32个滤波器&#xa;• 3x3卷积核&#xa;• ReLU激活" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=center;" parent="1" vertex="1">
          <mxGeometry x="120" y="440" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pool1" value="池化层1&#xa;• 2x2最大池化&#xa;• 降维处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=center;" parent="1" vertex="1">
          <mxGeometry x="240" y="440" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="conv2" value="卷积层2&#xa;• 64个滤波器&#xa;• 3x3卷积核&#xa;• ReLU激活" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=center;" parent="1" vertex="1">
          <mxGeometry x="360" y="440" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pool2" value="池化层2&#xa;• 2x2最大池化&#xa;• 特征压缩" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=center;" parent="1" vertex="1">
          <mxGeometry x="480" y="440" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="conv3" value="卷积层3&#xa;• 128个滤波器&#xa;• 3x3卷积核&#xa;• ReLU激活" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=center;" parent="1" vertex="1">
          <mxGeometry x="600" y="440" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="global-pool" value="全局池化&#xa;• 全局平均池化&#xa;• 特征聚合" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE0B2;strokeColor=#FF9800;fontSize=10;fontColor=#E65100;align=center;" parent="1" vertex="1">
          <mxGeometry x="720" y="440" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="triplet-section" value="Triplet Network层 - 孪生网络架构" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#9C27B0;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#6A1B9A;" parent="1" vertex="1">
          <mxGeometry x="80" y="530" width="1000" height="40" as="geometry" />
        </mxCell>
        <mxCell id="anchor" value="Anchor&#xa;锚点函数&#xa;基准样本" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=center;" parent="1" vertex="1">
          <mxGeometry x="200" y="590" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="positive" value="Positive&#xa;正样本&#xa;相似函数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=center;" parent="1" vertex="1">
          <mxGeometry x="350" y="590" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="negative" value="Negative&#xa;负样本&#xa;不相似函数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=center;" parent="1" vertex="1">
          <mxGeometry x="500" y="590" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="triplet-loss" value="Triplet Loss&#xa;三元组损失函数&#xa;d(a,p) + margin &lt; d(a,n)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#9C27B0;fontSize=11;fontColor=#6A1B9A;align=center;" parent="1" vertex="1">
          <mxGeometry x="650" y="590" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="output-section" value="向量化输出层 - 函数嵌入向量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=2;fontSize=14;fontStyle=1;fontColor=#C62828;" parent="1" vertex="1">
          <mxGeometry x="80" y="680" width="1000" height="40" as="geometry" />
        </mxCell>
        <mxCell id="embedding-vector" value="函数嵌入向量&#xa;• 512维向量&#xa;• 语义表示&#xa;• 相似性度量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=center;" parent="1" vertex="1">
          <mxGeometry x="200" y="740" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="vector-db-store" value="向量数据库存储&#xa;• 1200万函数向量&#xa;• HNSW索引&#xa;• 毫秒级检索" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=center;" parent="1" vertex="1">
          <mxGeometry x="400" y="740" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="similarity-calc" value="相似性计算&#xa;• 余弦相似度&#xa;• 欧氏距离&#xa;• 阈值判断" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#F44336;fontSize=11;fontColor=#C62828;align=center;" parent="1" vertex="1">
          <mxGeometry x="600" y="740" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="conn1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#1976D2;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="190" as="sourcePoint" />
            <mxPoint x="440" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#4CAF50;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="350" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="500" as="sourcePoint" />
            <mxPoint x="440" y="530" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conn4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#9C27B0;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="650" as="sourcePoint" />
            <mxPoint x="440" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cnn-conn1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="470" as="sourcePoint" />
            <mxPoint x="240" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cnn-conn2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="470" as="sourcePoint" />
            <mxPoint x="360" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cnn-conn3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="470" as="sourcePoint" />
            <mxPoint x="480" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cnn-conn4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="470" as="sourcePoint" />
            <mxPoint x="600" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cnn-conn5" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#FF9800;strokeWidth=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="470" as="sourcePoint" />
            <mxPoint x="720" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
