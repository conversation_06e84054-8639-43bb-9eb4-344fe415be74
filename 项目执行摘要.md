# 异构复杂软件供应链风险评估与安全治理研究项目下阶段执行摘要

## 项目概述

基于中期阶段取得的显著技术突破和超额完成的各项指标，下阶段将重点完善风险评估体系的全维度构建，深化与各课题间的技术融合，全面提升异构复杂软件供应链的风险识别、量化评估和安全治理能力。项目将针对AI生成代码安全检测、二进制多维度成分分析扩展、智能化风险量化评估等前沿技术难题实现重要突破，构建支撑6个重点行业应用示范的完整技术体系。

## 分课题技术成果

**课题1：面向源代码与二进制代码的软件成分分析技术研究**

通过扩展深度学习的异构二进制相似函数检测技术和优化增量代码片段基因标记算法，实现二进制函数特征向量数据库从1200万条扩展至2000万条、支持处理器架构从5种扩展至10种、二进制文件格式识别从27种扩展至50种，达到跨架构检测准确率提升至90%以上、处理效率提升3倍的技术水平，全面覆盖信创环境异构复杂场景的软件成分精准识别需求。

**课题2：针对异构复杂软件供应链的风险评估体系技术研究**

通过构建AI生成代码漏洞检测机制和基于机器学习的自适应风险量化模型，实现漏洞检查风险分析准确度从80%提升至90%、许可证合规风险分析准确度从80%提升至90%、威胁情报融合从当前数据源扩展至15个、AI代码漏洞样本库建设至500+个，达到支持新型威胁实时识别、多维度风险智能评估、动态权重自适应调整的全面风险管控能力，建立国内首个针对AI辅助开发场景的软件供应链安全评估体系。

**课题3：构建异构复杂软件供应链分析及风险评估平台技术研究**

通过优化分布式海量代码特征提取系统和完善智能化风险评估引擎，实现平台并发评估能力从当前水平提升至支持20个项目同时处理、单项目评估时间缩短至2分钟以内、系统可用性达到99.9%以上、数据处理能力达到10TB/日，达到支撑6个行业应用示范、服务1000+用户、处理百万级组件分析任务的大规模生产应用能力，构建国内技术领先的异构复杂软件供应链综合治理平台。

**课题4：开展异构复杂软件供应链安全治理的示范应用**

通过在金融、电信、能源、政务、制造业、高校等6个重点行业深化应用示范部署，实现用户满意度达到95%以上、安全事件发生率降低70%、平台稳定运行率达到99.5%以上、技术成果转化销售收入达到800万元，达到建立可复制推广的行业应用模式、形成完整的商业化服务体系、构建开源组件可信中心仓的产业化目标，为全国信创环境软件供应链安全治理提供标杆示范。

## 整体目标实现

项目下阶段完成后将建成国际先进、国内领先的异构复杂软件供应链风险评估与安全治理完整技术体系，形成涵盖11项软件著作权、6项授权发明专利、2篇高水平论文的丰富知识产权成果，全面提升我国在软件供应链安全领域的技术自主可控能力。通过技术创新突破和规模化产业应用，将为浙江省数字经济高质量发展和全国信创产业安全保障提供强有力的技术支撑，推动我国软件供应链安全技术达到国际领先水平。

