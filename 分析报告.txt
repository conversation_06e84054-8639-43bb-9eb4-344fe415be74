



浙江省“尖兵”“领雁”研发攻关计划
项目可行性报告及预算说明






项 目 名 称：异构复杂软件供应链风险评估与安全治理研究
  
　

　

　

申 请 日 期：2024年9月9日

目录
一、 国内外研究现状和技术发展趋势	4
1.1 对本项目背景的理解	4
1.2 软件成分分析技术研究现状	6
1.2.1 基于元数据的软件成分分析	6
1.2.2 基于源代码的软件成分分析	7
1.2.3 基于二进制代码的软件成分分析	8
1.3 软件供应链风险评估方法研究现状	9
1.3.1 软件供应链的威胁模型	10
1.3.2 软件供应链的风险识别与评估	10
1.4 软件成分分析产品国内外现状	13
1.5 研究现状总结	14
1.6 参考文献	17
二、 项目主要研发内容与实施方案	21
2.1 主要研发内容	21
2.1.1 课题一：面向源代码与二进制代码的软件成分分析关键技术	22
2.1.2 课题二：针对异构复杂软件供应链的风险评估体系	25
2.1.3 课题三：构建异构复杂软件供应链分析及风险评估平台	27
2.1.4 课题四：开展异构复杂软件供应链安全治理的示范应用	28
2.2 拟研究的科学问题	29
2.3 拟解决关键技术	30
2.4 主要创新点	32
2.5 技术路线与实施方案	33
2.5.1面向源代码与二进制代码的软件成分分析技术研究	33
2.5.2 针对异构复杂软件供应链的风险评估体系技术研究	60
2.5.3 构建异构复杂软件供应链分析及风险评估平台技术研究	81
2.5.4 开展异构复杂软件供应链安全治理的示范应用	93
三、 项目预期目标	97
3.1 主要技术经济指标	97
3.1.1 技术指标	98
3.1.2 经济指标	99
3.2 社会效益	100
3.3 技术应用	101
3.4 产业化前景	101
3.5 获取自主知识产权	102
四、 计划进度安排和课题分解	103
4.1 计划进度安排	103
4.2 课题分解	104
4.2.1 组织方式	104
4.2.2 项目管理机制	104
4.2.3 课题分解	105
五、 现有工作基础和条件	107
5.1项目申请单位情况	107
5.2项目组成员简介与研究成果	114
5.2.1 项目组成员简介	114
5.2.2 项目团队成员情况	115
5.2.3 研究理论基础	121
5.2.4 项目团队专利情况	126
5.2.5 项目团队相关软件著作权情况	128
5.2.6 承担单位现有软件成分分析产品情况	128



一、国内外研究现状和技术发展趋势
1.1 对本项目背景的理解
　　　随着我国数字化转型的加速推进，软件在推动经济社会发展中的核心作用日益凸显。其中，国产信创软件作为数字化进程建设的关键一环，在信创数字化建设工作中，发挥着举足轻重的作用。根据国资委信创79号文件规定：2027年前按顺序完成“2+8+N”的党政与八大重点行业100%信创替代。政府机关、央国企的信创数字化建设进入关键时期，信创产业加速向全栈化、全产业链、全行业迈进。
　　　然而，由于发展时间短、经验不足等因素，信创产业中的信创软件几乎都依赖开源软件进行构建，在软件供应链的链韧性和安全水平方面仍存在极大安全隐患。因此，迫切需要开展针对信创国产化场景下的异构复杂软件供应链风险评估与安全治理研究，为保障软件系统安全、应对复杂多变的威胁环境奠定坚实的软件基础。信创国产化场景下的软件供应链安全既是整个信创安全体系的基础，也是当前最需要补足的短板，信创国产化场景下的异构复杂软件供应链安全治理成为发展核心难题。
　　　针对上述问题与挑战，本项目拟选题“异构复杂软件供应链风险评估与安全治理研究”开展研究与验证。重点研究面向源代码与二进制代码的软件成分分析技术、异构复杂软件供应链风险评估体系、基于信创国产化场景的异构复杂软件供应链风险管控与治理平台三方面内容，并在国防军工、金融、运营商、能源、政务、制造业、高校等领域开展应用示范，实现平台研发与成果应用推广的良性循环。
　　　本项目将科学问题研究与实证测试相结合，形成系列算法、专利、软著与工具集，研究成果将整合并形成基于信创国产化场景的异构复杂软件供应链风险管控与治理平台，实现多维度高质量的代码特征快速提取、源代码和二进制代码成分分析、软件供应链风险评估体系、漏洞真实可达性检测及靶向热修复等系列成果。为信创产业软件供应链安全提供有力支撑，切实保障软件系统的供应链安全、数据安全和资产安全。


1.2 软件成分分析技术研究现状
1.2.1 基于元数据的软件成分分析
　　　基于元数据的代码成分分析是一种通过审查软件组件的元数据（如版本信息、许可证、作者等）来识别和评估这些组件及其相互关系的技术。这种方法可以用于漏洞检测，依赖性管理、确保许可证合规等方面，有助于开发者快速了解和管理项目中使用的各种第三方库和框架，从而提升软件的安全性[1]。Gonzalez [2]等人通过通过对程序元数据和字节码提取特征形成特征向量，可以在多种混淆级别下检测应用程序的相似性。Halder[3]等人从包的元数据信息中提取特征并进行分类，实现了对恶意软件包的检测。Márquez[4]等人通过构建一个关联漏洞的依赖图网络，并整合基于满足性模理论的形式模型，来分析和推断项目中可能存在漏洞。Lip[5]等人提出了一个名为PDGraph的方法，用于分析和映射软件项目依赖关系中的安全漏洞，以揭示和解决依赖重用库中已知漏洞的传播问题。Zhang[6]等人提出了Ranger工具，可以自动恢复Maven生态系统中依赖库的安全版本范围，解决了因上游库未更新导致的漏洞持续存在问题。Haryono[7]等人使用极端多标签学习技术，通过训练多个XML模型来从国家漏洞数据库漏洞报告中识别相关的受影响库。Wu[8]等人提出了一种名为Holmes的方法，通过学习排序技术来识别漏洞所影响的库，解决现有SCA工具在识别漏洞库时面临的数据库维护困难的问题。但由于元数据通常不包含运行时动态生成的数据。因此可能无法完全准确地反映出程序组件的真实使用情况和依赖情况，并且一但代码采用了一些混淆技术, 上述工具就无法识别相应的特征。


1.2.2 基于源代码的软件成分分析
　　　基于源代码的代码成分分析通过对代码包的源代码进行分析，并标记网络、文件系统、进程、代码生成相关的API，进一步将源代码解析为语法结构树并搜寻标记API的使用情况，最后分析代码数据流，检查代码数据流的源、sink节点和传播节点[9]。从而能够发现那些元数据方法可能遗漏的问题，如隐藏的功能、代码质量问题及安全漏洞。Andrade等人[10]提出了一个基于信息流控制的工具实现对Java 源代码的静态分析，以自动化发现提交代码是否存在访问隐私文件的风险。Staicu等人[11]通过静态分析源代码识别相关API的调用点，再基于分析结果实现脚本的模拟运行，来识别Node.js程序中由于特殊指令引入的注入攻击。Zhan等人[12]首先使用控制流图进行粗粒度的第三方库识别，然后通过提取基本块中的操作码进行精细化版本识别，提升了检测存在漏洞的第三方库版本的准确度。Xu等人[13]提出了LiDetector使用基于学习的方法，并采用概率上下文无关文法推断权利和义务，以进行许可证条款不兼容性检测以避免财务和法律风险。Fischer等人[14]将代码块表示为具有语义的特征向量，并计算与不安全代码块的相似度以代码安全性分析。Bilgin等人[15]将源代码转换为抽象语法树，再使用机器学习方法进行分析，完成了漏洞预测的下游任务。Yang等人[16]提出了一种名为ModX的框架，通过程序模块化技术将程序分解为基于功能的细粒度模块，并结合语法和语义特征，来检测软件中完全或部分引入的第三方库。Wu等人[17]使用带有注意力机制的图神经网络来学习漏洞代码的语义，再对源代码中漏洞的检测。但是，对于封闭源代码的软件产品，源代码通常不可用，这限制了该方法的适用性。此外，即使源代码可用，软件系统的复杂架构和依赖性可能导致分析过程复杂且难以执行，会影响分析的准确性和效率。
1.2.3 基于二进制代码的软件成分分析
　　　二进制软件成分分析（Binary Software Composition Analysis, SCA）根据分析方法和目标分为静态分析、动态分析、二进制到源代码的SCA、二进制到二进制的SCA及基于智能匹配的SCA。
　　　（1） 静态分析
　　　静态分析无需运行二进制文件，常用方法包括静态反汇编和多维特征比对。David等人[18]提出结合模糊测试和属性驱动测试的静态分析方法，利用软件组件的属性来发现安全漏洞。R. Duan等人[19]开发了基于相似性比较的技术，检测开源许可证违规及安全风险，通过提取字符串字面量和导出函数特征实现开源组件重用检测。BinPro[20]通过机器学习计算二进制与源代码的相似性，能够在不同编译器和优化级别下有效工作。B2SFinder工具[21]精准检测商业软件中的开源复用，通过静态分析结合加权匹配算法，识别COTS中的OSS。
　　　（2） 动态分析
　　　动态分析通过执行二进制文件获取运行时状态，通常比静态分析简单。LoPD方法[22]通过动态符号执行和定理证明技术检测程序语义差异。TOB框架[23]利用“胎记”特征检测软件抄袭行为，Gemini系统[24]则通过动态执行提取程序特征，识别跨架构相似代码，即使代码被混淆或优化也能有效工作。BinaryAI[25]通过两阶段方法捕获二进制代码的语法和语义特征，识别重用的第三方库。
　　　（3） 二进制到源代码的SCA
　　　此类方法通过二进制代码与源代码的匹配来识别开源组件。商业工具如Insignary Clarity、FlexNet Code Insight和Black Duck SCA[26]通过指纹匹配来检测开源库。RESource框架[27]通过匹配汇编代码和源代码库，实现高精度匹配。BinPro工具[28]通过控制流图和函数调用匹配，识别开源库的使用情况。B2SFinder工具[29]结合语法和语义相似性匹配技术，检测COTS软件中的开源复用。
　　　（4） 二进制到二进制的SCA
　　　此类技术通过二进制文件之间的相似性检测复用库。LibDB[30]通过函数调用图和深度学习嵌入，检测二进制文件中的第三方库，即使在编译和优化后也能保持高效的检测效果。Fossil系统[31]通过提取函数特征，与已知FOSS函数库进行匹配，识别二进制文件中的开源函数。跨模态检索方法[32]基于神经网络嵌入技术，提升二进制文件间的相似性检测精度。
　　　（5） 基于智能匹配的SCA技术
　　　智能匹配技术通过深度学习模型提取二进制和源代码的语法与语义特征。Gemini技术[33]通过图神经网络分析控制流图（CFG），在不同架构下检测代码相似性。OSSPolice[34]提取函数调用、控制流等特征，结合机器学习算法检测开源许可证违规及安全风险。Asm2vec[35]通过汇编代码转换为向量表示，提升在跨编译器优化和混淆情况下的相似性检测精度。CodeCMR[36]结合二进制和源代码特征，通过深度学习模型映射到共享向量空间，实现函数级匹配。
1.3 软件供应链风险评估方法研究现状
　　　软件供应链可被理解为一个软件在开发和运行过程中涉及的所有代码包（源码包或二进制包）、工具或平台（用于研发、测试、管理等），以及供方、需方等，按照依赖、组合等关系形成的供应关系网络。软件供应链风险评估体系旨在发现供应链短板，实现软件供应链的可溯性、可用性及可替代性。评估体系以软件产品在开发、交付和使用过程中的流转为主线，以下从威胁模型、风险识别与评估两个方面展开介绍。
1.3.1 软件供应链的威胁模型
　　　Zhou等人[37]总结软件供应链的威胁主要是两大方面：(1)攻击者通过伪装和篡改的方式对软件供应链中产出的软件进行污染；(2)攻击者通过这些被污染的软件获得了在软件运行环境中执行操作的权限，进一步造成信息泄露等危害。Linux开源软件基金会(OpenSSF)[38]从本地开发、外部代码贡献、代码中心仓库测试集成、软件包消费等软件供应链阶段分别进行威胁建模，分析了各环节的25种潜在攻击方式。谷歌公司提出的威胁模型主要基于他们总结的供应链框架即“软件构件的供应链级别”(Supply Chain Levels for Software Artifacts, SLSA)，更加关注软件供应链的完整性[39]。类似地，MITRE组织[40]提出的供应链缺陷主要涵盖对开发工具、开发环境、公开或私有的源代码仓库等真实攻击对象的操控，同时将政治、社会、法律等方面的风险因素纳入考虑。而Torres-Arias等人[41]和Peterson等人[42]特别研究了开源给软件供应链带来的新兴威胁，包括意外引入的开源漏洞、恶意引入的开源漏洞和引入带有恶意逻辑的开源组件。其中Torres-Arias等人还指出了供应链中各个环节的环境被攻陷及供应方信任凭证失窃等风险。
1.3.2 软件供应链的风险识别与评估
　　针对新型开源协作模式下复杂开源供应链带来的风险，安全人员针对各个环节的攻击面设计了风险识别技术。以下将各个环节的风险识别和评估总结为以下三个关键研究方向：软件供应链中第三方组件的风险识别、软件供应链视角下的应用软件风险识别及下载更新过程的风险识别。
1.3.2.1 软件供应链中第三方组件的风险识别
　　　随着PyPI、NPM、RubyGem等第三方组件分发市场的广泛使用，包名抢注等恶意包注入问题日益突出。为应对这类问题，Taylor等人[43]首先发现，包名抢注攻击的常见特征包括攻击目标通常是流行包，且恶意包的名称与原始包极为相似。基于此，他们开发的TypoGuard利用名称相似性模型，并结合包的流行度特征来检测恶意包。当一个包的名称与流行包相似但其使用频率较低时，系统会将其标记为潜在的恶意包。TypoGuard虽能有效检测相似包名的攻击，但对流行度的依赖可能会导致部分攻击未被识别。Python的PyPI包管理器引入了PyPi-parker[44]模块，进一步优化了恶意包检测机制。该模块通过维护一个恶意包黑名单，在用户下载时若包名与黑名单中的记录相似，系统会发出警告。黑名单机制简洁实用，但只能识别已知恶意包，对于未知的新型攻击仍然存在盲区。与上述方案不同，Vu等人[45]提出了一种不同的解决思路，采用哈希比较来检测恶意包。他们通过比较包管理器中的包与Github源代码库中的包，利用包的哈希值差异进行检测。这种方法有效提升了检测精度，克服了对名称相似性的依赖。然而，依赖源码库的特性增加了操作的复杂性，需在精确性与操作负担之间做出权衡。
1.3.2.2 软件供应链视角下的应用软件风险识别
　　本节聚焦于软件供应链视角下特有的软件风险及对应的风险识别方案，包含应用软件中第三方组件检测及其风险识别和分发市场中的恶意软件识别。
　　　（1）应用软件中的第三方组件检测及其风险识别
　　　目前，开发软件系统对第三方组件的依赖日益加深，检测其内部依赖关系是开发者、维护者和使用者理解系统架构及管理风险的关键。Tellnes等人[46]提出，依赖过多第三方库可能带来安全性和可用性的隐患，系统的安全性很大程度上取决于第三方组件的生态。因此，研究人员从不同角度探索了不同的检测方法。
　　　一是基于元数据的静态检测，如OWASP Dependency Check 工具[47]，通过扫描项目中的依赖项信息来识别潜在的漏洞，利用CPE标识符进一步确定是否存在已知漏洞。该方法依赖于特定字段信息，若这些字段被修改，检测准确性将降低。为此，Cadariu等人[48]扩展了该工具，针对Java系统的依赖关系进行了改进，通过更精确的CPE匹配提高了检测的有效性。由于元数据无法全面反映依赖关系，尤其在代码经过混淆处理时，基于源代码的静态检测成为一种更为精细的手段。例如，LIBSCOUT利用哈希匹配识别混淆后的库特征，确保在复杂环境下仍能精准定位组件版本。Atvhunter则针对易受攻击的组件版本进行识别，并提供详细的漏洞信息。
　　　（2）分发市场中的恶意应用软件识别
　　　基于元数据的检测方案是最简单的，检测速度快，但准确率较低。Hemel等人[49]开发了BAT，用于检测物联网固件源代码和二进制代码的相似性，通过比较字符串、压缩数据及二进制增量大小来实现检测。尽管该方法检测速度快，但误报率较高，存在漏洞。
　　　相比之下，基于相似度函数的检测方案通过使用哈希技术提升了准确率。Zhou等人[50]在安卓应用分发市场中发现了重打包攻击，并开发了DroidMOSS系统，该系统利用模糊哈希技术为应用程序生成指纹，通过相似性计算检测重打包行为，尽管速度快，但对混淆代码的检测准确性较低。
　　　Golubev等人[51]则采用了SourcererCC[52]的相似性检测，利用针对代码块的比较算法，尤其在软件许可证违规检测上表现出色，显著提高了检测的有效性。
1.3.2.3 下载更新过程的风险识别
　　　面对用户在下载和更新软件时可能存在网络劫持、钓鱼网站等风险，研究人员主要研究了更新下载渠道的风险识别。Garrett等人[53]提出异常检测的方案，来识别恶意更新的Node。JS软件包。相比Garrett等人的方案，Teng等人[54]更加聚焦网络劫持的风险识别。Teng等人指出当前开源软件供应链中软件下载更新环节缺乏对更新信息和更新包的认证是引入风险的根本原因。因此，Teng等人设计了一种基于流量分析的软件更新漏洞自动检测和验证方法。该方法通过提取软件更新过程中的网络流量，对升级机制进行自动画像，并将其与漏洞特征向量匹配，来预判潜在的漏洞。
1.4 软件成分分析产品国内外现状
　　　国内软件成分分析厂商众多，但与国际上头部代表厂商（Synopsys、Mend 原WhiteSource、FOSSA）在核心技术上仍存在较大差距。
表 1 国内成分分析领域核心技术掌握情况
技术名称	细分技术名称	技术简介	重要程度	进展程度	国内掌握情况
源码特征文件成分识别技术	基于包管理器的分析方式	基于项目组件包管理文件的特性，在项目构建过程中，针对组件特征的提取方法以及包与包之间的关联性，通过引擎算法分析出被测项目的开源组件依赖关系树，形成项目的SBOM文件。	3	5	国内已掌握
　　　	模拟组件依赖关系分析方式	通过模拟项目构建的过程得出最终开源组件依赖关系	3	5	国内已掌握，且已应用于产品
代码片段识别技术	基于代码片段的开源成分识别技术	用于识别软件中以源代码片段方式使用部分开源代码的方法	5	3	未完全掌握。国内基本掌握基础级分析技术，但对于语义化的高阶代码识别处于理论阶段，数据能力不足（源数据量较少）
镜像识别技术	镜像包分析技术	以tar文件形式的镜像包进行内部成分分析	4	4	分析技术基本掌握
　　　	公共镜像风险分析	通过装载镜像对其所有关联层级镜像扫描分析	4	4	分析技术基本掌握
固件与二进制格式识别技术	固件特征字符串识别技术	固件文件进行二进制全文特征匹配，获取固件的具体信息	4	2	未掌握。固件类型种类繁多，国内掌握相关格式较少
二进制文件成分分析技术	基于字符特征识别技术	对目标代二进制码字符特征识别，来实现已经编译的二进制文件的识别，获取开源引入成分	2	5	基本掌握。国内针对部分语言已经技术实现，但识别率低
	数据流控制流特征识别技术	对标代码片段识别，来实现已经编译的二进制文件的识别，获取开源引入成分	5	2	未掌握。国内针对部分语言已经技术实现，但误报率高，数据能力不足
自动化数据监控采集技术	动态组件漏洞对齐方式	监控安全网站漏洞信息，进行归并和梳理，同步至漏洞数据库，确保漏洞信息的一致性和完整性；自动化获取各大开源社区和在线组件仓库的组件元素，并建立一个完整的开源组件信息中心	5	3	国内基本掌握。数据监控与采集技术发展时间久、能力基本成熟。
漏洞可达性分析技术	可达性分析技术	漏洞可达性分析能够判断漏洞是否会被自研代码触发，该技术的应用能大幅降低漏洞误报率。	5	2	未掌握。该分析技术要求高、难度大，国内对该技术研究时间短、相关实证研究少，导致该技术研究相对匮乏，未能掌握。

　　　
1.5 研究现状总结
　　　当前的软件供应链安全研究，特别是围绕源代码和二进制代码的软件成分分析（SCA），已经取得了一定的进展，但在面对复杂的开源软件供应链时仍存在诸多挑战。与此同时，由于信创国产化环境相对封闭，学术界对异构复杂软件供应链风险的研究相对较少，产业界对信创国产化环境软件供应链安全挑战主要可以总结为以下几点：
1.现有外国软件成分分析产品无法支撑信创环境，且存在安全隐患。国外相关产品无法支撑国产信创复杂环境的兼容适配、编译分析。同时，国外产品对于信创软件、国产开源项目等数据覆盖不足，难以实现基础软件信息及安全风险的识别与分析。其次，国外产品存在自主可控问题，软件自身安全性、敏感信息保护、数据安全等问题存在隐患，且受到地缘政治影响，极易发生贸易管制、供应商断供等风险。
2.国产软件成分分析产品二进制分析检出率低。信创国产化场景下，国内现有二进制分析类产品普遍存在着以下痛点问题：
(1)信创产业所使用的二进制软件成品格式复杂，相关对二进制文件格式兼容不足，导致无法解包各类结构复杂多样的二进制文件；
(2)信创环境中国产处理器、操作种类多，对复杂环境跨平台二进制软件成品难以反编译、逆向分析困难；
(3)几乎采用技术门槛最低的二进制字符特征分析技术，缺少核心能力；导致检测检出率真极低。
3.国产软件成分分析产品二进制分析误报率高。
4.复杂软件环境导致风险难以修复。信创国产化场景下，行业用户软件通常以采购或外包形式引入软件，缺少完整源码数据，导致无法通过常见的源码检测识别软件成分信息；同时，由于下游供应商维护到期、发生供应链中断等原因，对于已知的开源漏洞威胁也难以修复；
5.异构复杂环境下的软件供应链风险无法量化评估。软件供应链风险涵盖开源漏洞风险、开源许可证风险、供应链投毒风险、软件质量等多维度评估指标。国内与国际均未出台、总结相关相关评估体系，无法对软件的安全性、健康度、可持续性多维度综合量化评估。
　　　


1.6 参考文献
[1]Hejderup J, Beller M, Triantafyllou K, et al. Präzi: from package-based to call-based dependency networks[J]. Empirical Software Engineering, 2022, 27(5): 102.
[2]Gonzalez H, Stakhanova N, Ghorbani A A. Droidkin: Lightweight detection of android apps similarity[C]//International Conference on Security and Privacy in Communication Networks: 10th International ICST Conference, SecureComm 2014, Beijing, China, September 24-26, 2014, Revised Selected Papers, Part I 10. Springer International Publishing, 2015: 436-453.
[3]Halder S, Bewong M, Mahboubi A, et al. Malicious Package Detection using Metadata Information[C]//Proceedings of the ACM on Web Conference 2024. 2024: 1779-1789. 
[4]Márquez A G, Varela-Vaca Á J, López M T G, et al. Vulnerability impact analysis in software project dependencies based on Satisfiability Modulo Theories (SMT)[J]. Computers & Security, 2024, 139: 103669. 
[5]Alfadel M, Costa D E, Shihab E, et al. On the discoverability of npm vulnerabilities in node. js projects[J]. ACM Transactions on Software Engineering and Methodology, 2023, 32(4): 1-27. 
[6]Zhang L, Liu C, Chen S, et al. Mitigating persistence of open-source vulnerabilities in maven ecosystem[C]//2023 38th IEEE/ACM International Conference on Automated Software Engineering (ASE). IEEE, 2023: 191-203.
[7]Haryono S A, Kang H J, Sharma A, et al. Automated identification of libraries from vulnerability data: Can we do better?[C]//Proceedings of the 30th IEEE/ACM International Conference on Program Comprehension. 2022: 178-189.
[8]Wu S, Song W, Huang K, et al. Identifying Affected Libraries and Their Ecosystems for Open Source Software Vulnerabilities[C]//Proceedings of the IEEE/ACM 46th International Conference on Software Engineering. 2024: 1-12. 
[9]Duan R, Alrawi O, Kasturi R P, et al. Towards measuring supply chain attacks on package managers for interpreted languages[J]. arXiv preprint arXiv:2002.01139, 2020. 
[10]Andrade R, Borba P. Privacy and security constraints for code contributions[J]. Software: Practice and Experience, 2020, 50(10): 1905-1929. 
[11]Staicu C A, Pradel M. Freezing the Web: a study of {ReDoS} vulnerabilities in {JavaScript-based} web servers[C]//27th USENIX security symposium (USENIX Security 18). 2018: 361-376.
[12]Zhan X, Fan L, Chen S, et al. Atvhunter: Reliable version detection of third-party libraries for vulnerability identification in android applications[C]//2021 IEEE/ACM 43rd International Conference on Software Engineering (ICSE). IEEE, 2021: 1695-1707.
[13]Xu S, Gao Y, Fan L, et al. Lidetector: License incompatibility detection for open source software[J]. ACM Transactions on Software Engineering and Methodology, 2023, 32(1): 1-28.
[14]Fischer F, Böttinger K, Xiao H, et al. Stack overflow considered harmful? the impact of copy&paste on android application security[C]//2017 IEEE symposium on security and privacy (SP). IEEE, 2017: 121-136.
[15]Bilgin Z, Ersoy M A, Soykan E U, et al. Vulnerability prediction from source code using machine learning[J]. IEEE Access, 2020, 8: 150672-150684.
[16]Yang C, Xu Z, Chen H, et al. ModX: binary level partially imported third-party library detection via program modularization and semantic matching[C]//Proceedings of the 44th International Conference on Software Engineering. 2022: 1393-1405.
[17]Wu B, Liu S, Xiao Y, et al. Learning Program Semantics for Vulnerability Detection via Vulnerability-Specific Inter-procedural Slicing[C]//Proceedings of the 31st ACM Joint European Software Engineering Conference and Symposium on the Foundations of Software Engineering. 2023: 1371-1383.
[18]David et al., "Fuzzing with Property-Driven Fuzz Testing," in Proceedings of the 38th ACM SIGPLAN Conference on Programming Language Design and Implementation (PLDI '17). 
[19]Duan et al., "Identifying Open-Source License Violation and 1-Day Security Risk at Large Scale," in Proc. of ACM CCS, 2017. 
[20]Miyani D, Huang Z, Lie D. Binpro: A tool for binary source code provenance. arXiv preprint arXiv:1711.00830, 2017. 
[21]Yuan Z, Feng M, Li F, et al. B2SFinder: Detecting Open-Source Software Reuse in COTS Software. 2019 34th IEEE/ACM International Conference on Automated Software Engineering (ASE). IEEE, 2019: 1038-1049.
[22]Ming, F. Zhang, D. Wu, P. Liu, and S. Zhu, "Deviation-based Obfuscation-Resilient Program Equivalence Checking with Application to Software Plagiarism Detection," IEEE Transactions on Reliability, vol. 65, no. 4, 2016. 
[23]Tian, T. Liu, Q. Zheng, E. Zhuang, M. Fan, and Z. Yang, "Reviving Sequential Program Birthmarking for Multithreaded Software Plagiarism Detection," IEEE Transactions on Software Engineering, vol. 44, no. 5, 2017. 
[24]Xiaojun Xu, Chang Liu, Qian Feng, Heng Yin, Le Song, and Dawn Song, "Gemini: Fast and Accurate Cross-Architecture Binary Code Similarity Detection," in Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security (CCS '17). 
[25]Ling Jiang, Junwen An, Huihui Huang, Qiyi Tang, Sen Nie, Shi Wu, and Yuqun Zhang. BinaryAI: Binary Software Composition Analysis via Intelligent Binary Source Code Matching. In 2024 IEEE/ACM 46th International Conference on Software Engineering (ICSE).
[26]Commercial product websites.
[27]Rahimian A, Charland P, Preda S, et al., "RESource: A Framework for Online Matching of Assembly with Open Source Code," in International Symposium on Foundations and Practice of Security. 2012.
[28]Miyani D, Huang Z, Lie D., "BinPro: A Tool for Binary Source Code Provenance," arXiv preprint arXiv:1711.00830 (2017). 
[29]Zimu Yuan, Muyue Feng, Feng Li, Gu Ban, Yang Xiao, Shiyang Wang, Qian Tang, He Su, Chendong Yu, Jiahuan Xu, et al. B2SFinder: Detecting Open-Source Software Reuse in COTS Software. In 2019 34th IEEE/ACM International Conference on Automated Software Engineering (ASE).
[30]Xiaojun Xu, Chang Liu, Qian Feng, Heng Yin, Le Song, and Dawn Song, "Gemini: Fast and Accurate Cross-Architecture Binary Code Similarity Detection," in Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications Security (CCS '17).
[31]Zeping Yu, Wenxin Zheng, Jiaqi Wang, Qiyi Tang, Sen Nie, Shi Wu, "CodeCMR: Cross-Modal Retrieval For Function-Level Binary Source Code Matching," 34th Conference on Neural Information Processing Systems (NeurIPS 2020), Vancouver, Canada.
[32]Wei Tang, Yanlin Wang, Hongyu Zhang, Shi Han, Ping Luo, and Dongmei Zhang. LibDB: An Effective and Efficient Framework for Detecting Third-Party Libraries in Binaries. 22 
[33]R. Duan, A. Bijlani, M. Xu, T. Kim, and W. Lee, "Identifying Open-Source License Violation and 1-day Security Risk at Large Scale," in Proc. of the 2017 ACM SIGSAC Conf. on Computer and Communications Security, 2017.
[34]Liu B, Huo W, Zhang C, et al. αdiff: Cross-version binary code similarity detection with dnn. 33rd ACM/IEEE International Conference on Automated Software Engineering. 2018: 667-678. 
[35]S. Woo, S. Park, S. Kim, H. Lee, and H. Oh, "CENTRIS: A Precise and Scalable Approach for Identifying Modified Open-Source Software Reuse," in Proc. of the 43rd IEEE/ACM Int'l Conf. on Software Engineering (ICSE), 2021.
[36]X. Xu, C. Liu, Q. Feng, H. Yin, L. Song, and D. Song, "Neural Network-based Graph Embedding for Cross-Platform Binary Code Similarity Detection," in Proc. of the 2017 ACM SIGSAC Conf. on Computer and Communications Security, 2017. 
[37]Zhenfei Z. Research on Pollution Mechanism and Defense of Software Supply Chain. Beijing University of Posts and Telecommunications. 2018. 
[38]OpenSSF. Identifying Security Threats in Open Source Projects. https://github.com/ossf/wg-identifying-security-threats.
[39]Google. Introducing SLSA, an End-to-End Framework for Supply Chain Integrity. https://security.googleblog.com/2021/06/int.
[40]CISA. Supply Chain Compromise. https://www.cisa.gov/supply-chain-compromise.
[41]Torres-Arias S, Afzali H, Kuppusamy T K, et al. In-Toto: Providing Farm-to-Table Guarantees for Bits and Bytes.
[42]WhiteSource. Software Supply Chain Attacks. https://www.whitesourcesoftware.com/resources/blog/software-supply-chain-attacks/.
[43]Taylor M, Vaidya R, Davidson D, et al. Defending Against Package Typosquatting. International Conference on Network and System Security, 2020: 112–131.
[44]Bullock M. Pypi-Parker[M/OL].  https://github.com/mattsb42/pypi-parker.
[45]Vu D L, Pashchenko I, Massacci F, et al. Towards Using Source Code Repositories to Identify Software Supply Chain Attacks. ACM
[46]Tellnes J. Dependencies: No Software Is an Island. Master. Master's thesis, 2013.
[47]OWASP. OWASP Dependency-Check Project. https://owasp.org/www-project-dependency-check/.
[48]Cadariu M, Bouwers E, Visser J, et al. Tracking Known Security Vulnerabilities in Proprietary Software Systems. International Conference on Software Analysis, Evolution, and Reengineering, 2015.
[49]Hemel A, Kalleberg K T, Vermaas R, et al. Finding Software License Violations through Binary Code Clone Detection. Working Conference on Mining Software Repositories, 2011: 63–72. Conference on Mining Software Repositories, 2011: 63–72.
[50]Zhou W, Zhou Y, Jiang X, et al. Detecting Repackaged Smartphone Applications in Third-Party Android Marketplaces. ACM Conference on Data and Application Security and Privacy, 2012: 317–326. 
[51]Golubev Y, Eliseeva M, Povarov N, et al. A Study of Potential Code Borrowing and License Violations in Java Projects on GitHub. International Conference on Mining Software Repositories, 2020: 54–64. 
[52]Sajnani H, Saini V, Svajlenko J, et al. SourcererCC: Scaling Code Clone Detection to Big-Code. International Conference on Software Engineering, 2016: 1157-1168.
[53]Garrett K, Ferreira G, Jia L, et al. Detecting Suspicious Package Updates. International Conference on Software Engineering: New Ideas and Emerging Results, 2019.
[54]Teng J, Guang Y, Shu H, et al. Automatic Detection Method for Software Upgrade Vulnerabilities based on Traffic Analysis. Chinese Journal of Network and Information Security, 2020, 6(01): 94–108.


二、项目主要研发内容与实施方案
2.1 主要研发内容
　　　针对国产信创环境下软件供应链底层组件自主可控度低、现有软件成分分析技术能力不足和风险管控与安全测评能力不足等问题，本项目将在现有技术积累基础上，进行面向源代码与二进制代码的软件成分分析技术和异构复杂软件供应链风险评估体系研究，重点在代码特征快速提取技术研究，包括基于增量代码片段基因标记技术的源代码特征快速提取技术研究、基于自监督学习的跨语言代码克隆检测方法研究、基于大语言模型开源组件漏洞缺陷提取技术研究；适用于二进制代码特征快速提取技术研究、多源异构供应链软件威胁建模技术研究、多维度风险指标体系构建方法研究、基于源代码漏洞可达性分析技术研究、二进制软件包多维度分析评估技术研究和靶向热修复技术等关键技术上寻求突破，最终形成基于信创国产化场景的异构复杂软件供应链风险管控与治理平台，在代码和许可证特征快速提取、软件包成分精准识别、软件潜在风险多维度量化评估、漏洞真实可达性检测、组件风险监控预警和靶向热修复等方面建立多语言覆盖的高可靠、高性能、强通用性的软件供应链安全风险管控与治理能力。

　　　如下图所示，项目总体研究内容划分为四个部分：
　　　　　（1）面向源代码与二进制代码的软件成分分析技术。
　　　　　（2）针对异构复杂软件供应链的风险评估体系。
　　　　　（3）构建异构复杂软件供应链分析及风险评估平台。
　　　　　（4）开展异构复杂软件供应链安全治理的示范应用。

图 1 总体研究内容
2.1.1 课题一：面向源代码与二进制代码的软件成分分析关键技术
(1)代码特征快速提取方法研究
　　　本部分针对信创国产化环境下软件供应链复杂异构性和海量数据处理需求，拟开展代码特征快速提取方法研究，提升软件成分分析效率、准确性和自动化水平。主要包括：
　　　1)基于增量代码片段基因标记技术的源代码特征快速提取技术
　　　针对代码克隆同源检测中的复杂性问题，解决全量代码基因提取效率低、数据冗余以及处理增量代码变更的高成本，实现基于增量代码片段基因标记的快速特征提取技术，应对开源项目高频增量更新和大规模代码库的代码克隆检测场景。
　　　2)适用于二进制代码特征快速提取技术研究
　　　针对信创环境下二进制软件，解决无法获取源码、异构编译影响相似性的问题，实现二进制代码中快速识别安全漏洞函数，应对跨平台、跨架构、编译优化等复杂场景。
　　　3)基于大语言模型开源组件漏洞缺陷提取技术研究
　　　针对开源组件漏洞修复行为的学习，解决自动获取漏洞关联函数的问题，实现基于大语言模型的Java缺陷函数修复行为分析，应对代码漏洞可达性分析及漏洞修复支持的复杂场景。
(2)源代码软件成分分析技术研究
　　　1)基于自监督学习的跨语言代码克隆检测方法研究
　　　针对跨语言代码克隆检测中由于语言差异和语法结构复杂导致的检测困难的问题，研究高阶代码克隆检测方法，使用抽象语法树解析不同语言的代码，利用同义词转换减少语法差异，并采用树卷积神经网络提取抽象语法树特征，捕捉代码结构和语义，通过自监督学习，最小化克隆代码对的距离、最大化非克隆对的距离，实现了跨语言、复杂语法结构下的代码克隆检测。
　　　2)基于关键阻塞链识别的漏洞修复传播路径分析方法研究
　　　针对漏洞修复在包管理器生态系统中的传播仍存在严重滞后的问题，研究识别阻碍漏洞修复传播的关键阻塞链的分析方法，通过建立漏洞和包管理器依赖关系的元数据模型，分析传播关键阻塞链特征，实现漏洞修复关键阻塞链的精准识别。该方法可为包开发者提供详细信息，提醒他们注意修复版本更新的滞后情况及漏洞对包管理器生态系统的影响，加速漏洞修复的传播速度。
(3)二进制代码软件成分分析技术研究
　　　1)基于文件字节特征的二进制启发式解包技术研究
　　　针对信创环境下多样化二进制软件封装格式，解决传统文件后缀和文件头识别方法不足的问题，实现基于字节特征的启发式解包技术，应对行业专有格式、多重封装和加壳文件的复杂场景。
　　　2)基于深度学习的二进制函数向量相似度模型技术研究
　　　针对信创软件环境下供应商以二进制等复杂交付场景，常见源码分析困难、开源成分不清、漏洞危害难以溯源、法律侵权风险难以鉴别等核心难点，研究二进制软件包多维度分析评估技术，该技术综合利用不同特征的识别方法，并将识别结果汇总，生成一个由不同维度依据组成的检测结果，从而保证高准确率和高检出率。具体涵盖：
　　　基于深度学习模型的异构二进制相似函数检测技术。针对信创环境下不同操作系统平台、不同处理器、不同编译器导致的函数差异化，对差异化函数进行相似度检测的需求，研究异构二进制相似函数检测技术。
　　　基于图匹配的函数调用关系检测技术。针对海量相似函数情况下，从而导致出现无关相似函数的问题，对无关相似函数去冗、聚合的需求，研究基于图匹配的函数调用关系检测技术。
　　　基于二进制字符串分词特征的检测技术。针对二进制软件包快速特征提取与快速成分检测、以及多种检测技术交叉验证的需求，研究基于二进制字符串分词特征的检测技术。
　　　3)面向函数内联场景的二进制到源代码函数相似性检测方法
　　　针对函数内联场景下的二进制到源代码函数匹配需求，研究面向一对多匹配的二进制到源代码函数相似性检测方法，解决二进制到源代码匹配时因函数内联导致的性能损失，实现二进制到源代码函数相似性检测方法在内联场景下检测能力的有效性能提升。该方法可搜索内联源代码函数，提升现有二进制到源代码函数相似性检测能力，帮助现有软件成分分析工具更好地应对内联挑战。
　　　4)基于模块化语义匹配的二进制代码第三方库模块检测方法
　　　针对现有第三方库检测方法依赖句法特征，鲁棒性不足的问题，研究基于模块化语义匹配的二进制第三方库模块检测方法，通过同时提取模块化的语法和语义特征，度量模块间的距离，检测二进制程序中的相似库模块复用，实现程序模块的连贯识别，提升第三方库模块识别准确度。
2.1.2 课题二：针对异构复杂软件供应链的风险评估体系
　　　聚焦软件中引入存在漏洞的第三方组件潜在风险量化评估难题，本课题拟围绕海量异构复杂供应链软件风险评估所需的通用性、高效性、安全性和可靠性等要求开展安全风险威胁建模、指标构建及评估方法研究。为软件安全威胁治理，实现漏洞告警和安全左移提供基础。具体如下：
(1)多源异构供应链软件威胁建模
　　　针对异构供应链软件数据来源多样、语言版本不同、语法不一致而导致的建模困难的特点，提出一种灵活的威胁建模机制，处理不同数据源和结构之间的差异，基于违规许可证检出等数据处理技术构建统一的数据抽象层，实现跨源数据的规范化处理。在该抽象层中，引入通用的语义标准与安全策略，确保各类数据在传输与处理过程中的一致性。同时，通过融合和富集算法，围绕目标对象构建完整的属性集合，全面反映研究对象的特性，确保研究对象的完整性。实现通用和高扩展的多源软件攻击威胁建模。
(2)多维度供应链风险指标体系构建
　　　针对异构复杂供应链中的多重风险因素，提出一种构建多维度供应链风险指标体系的方法。旨在识别并量化软件供应链各个环节的潜在风险源，设计涵盖技术、管理、知识产权、可维护性和闭源组件等多个维度。通过对供应链系统中各个环节的风险点进行全面分析，基于定量与定性相结合的方式，定义不同维度的风险指标，全方位地评估软件供应链的健康状况和潜在威胁。依据软件组件的来源（如开源、闭源）选择相应的评估指标，并针对闭源组件，综合代码特征与二进制代码成分分析结果，额外设计二进制代码维度下的评估指标，进行针对性的风险评估，以确保评估结果的准确性和全面性。
(3)异构供应链风险评估方法研究
　　针对现有供应链风险评估方法误报率高、二进制软件量化评估困难等难题，提出一套可行高效的技术解决方案。大幅度提升供应链整体风险评估的准确性、实时性和可操作性，为供应链管理提供有力的技术支持。具体如下：
　　1）基于漏洞可达性分析的风险评估优化：针对现有风险评估技术导致的误报问题，提出一种基于漏洞可达性分析的风险精准评估方法，构建漏洞可达性路径，对系统中每个漏洞的潜在威胁进行深入分析，评估攻击者成功利用漏洞的可能性。深入分析组件修复代码、漏洞PoC/Exp以及漏洞原理，建设漏洞可达性规则模板。研究并采用漏洞可达性验证技术，对项目源代码进行抽象语法树（AST）分析和配置文件解析，验证是否存在漏洞的可达路径或配置缺陷。进一步优化风险评估模型。降低因评估误报而引入的时间成本，实现具备高效性的复杂供应链风险评估。
　　2）基于二进制软件包多维度的异构软件分析评估：针对信创软件环境下供应商二进制软件复杂交付场景，开源代码分析难、溯源难、鉴别难等难题，提出一种基于二进制软件包多维度的分析评估方法。从二进制函数、函数调用关系、二进制字符串三个维度，多维度分析二进制中的软件成分组成，并进行交叉验证，提高二进制检测的准确度。在二进制函数维度上，研究基于深度学习模型的异构二进制相似函数检测技术，解决信创环境下不同操作系统平台、不同处理器、不同编译器导致的函数差异化问题，满足差异化函数相似度检测的需求。在函数调用关系维度上研究基于图匹配的函数调用关系检测技术，解决海量相似函数导致的无关相似函数的问题，满足无关相似函数去冗、聚合的需求。在二进制字符串维度上，研究基于二进制字符串分词特征的检测技术，满足二进制软件包快速特征提取与快速成分检测、以及多种检测技术交叉验证的需求。通过针对二进制软件包的多维度交叉验证与评估，对二进制软件的整体质量、稳定性、安全性进行量化分析，为异构供应链中的二进制软件选择和管理提供可靠依据。
2.1.3 课题三：构建异构复杂软件供应链分析及风险评估平台
(1)构建海量开源项目监控与源数据采集系统
　　　针对信创复杂场景下海量源代码与二进制代码的监控与采集，解决自动化监控和增量更新采集问题，实现面向多数据源的代码特征基因库构建技术，应对开源组件、供应链软件、闭源软件等数据源的实时监控与高效采集需求。
(2)构建分布式海量代码特征基因提取系统
　　　针对海量代码处理，解决代码特征基因提取，实现高效的分布式存储与处理，应对大规模代码分析和特征提取场景。
(3)构建基于大语言模型识别开源组件漏洞缺陷提取系统
　　　针对开源组件漏洞修复，解决漏洞函数的自动识别与修复行为学习，实现漏洞修复模式的深度分析与预测，应对漏洞关联函数识别和代码修复优化场景。
2.1.4 课题四：开展异构复杂软件供应链安全治理的示范应用
　　　在信创国产化的背景下，异构复杂软件供应链的安全问题日益突出，尤其是涉及到国家关键基础行业（如军工、金融、运营商、能源、政务、制造业、高校等）的软件供应链，任何风险都可能导致严重的安全事件。针对这些行业的异构复杂软件供应链安全治理的示范应用场景研究，不仅有助于提高各行业的软件供应链安全防护水平，还能为其他行业提供参考和借鉴。本课题的重点是通过示范应用验证异构复杂软件供应链安全治理方案的有效性和适用性，并通过在不同行业的应用中总结经验，形成可推广的安全治理策略和方法。本课题的主要研发内容为：
　　　（1）平台集成与优化
　　　优化现有的二进制分析技术和自研率分析工具，与软件开发生命周期中的CI/CD、OA等第三方管理平台进一步融合，确保安全检测能够在开发的各个阶段自动进行。提升自动化测试的精确度，减少假阳性和假阴性，确保对自研软件的安全性分析更为准确。建立实时反馈机制，将安全检测结果直接反馈给开发人员，提供即时修复建议和自动化修复工具，提高开发效率和安全性。
　　　（2）风险评估流程优化
　　　进一步优化外包软件的风险评估模型，增加对外包软件安全性及其组件依赖性的深度分析，识别潜在的安全漏洞和风险点。提高自研率分析的精确度，确保能够准确区分外包组件与自研组件，评估其对整体软件安全的影响。开发自动化的报告生成工具，将评估结果以易于理解的报告形式提供给相关决策者，以支持验收过程中的决策。
　　　（3）固件分析与验证能力
　　　提升固件的二进制分析技术，支持对固件中的复杂嵌入式代码进行深度分析，识别潜在的安全漏洞。开发面向固件的二进制风险评估工具，能够综合评估固件在不同应用场景下的安全性，提供定制化的风险评估和缓解建议。在实际硬件设备中进行集成测试，验证分析工具在不同硬件环境中的有效性和适应性，确保设备在实际使用中的安全性。
2.2 拟研究的科学问题
　　　各行业的异构复杂软件供应链通常包含多种操作系统、编程语言以及硬件架构，形成了一个复杂且动态变化的生态系统。如何实现对这些异构环境中的软件成分进行精确的安全分析，识别潜在风险，并将安全治理策略在实际应用中有效落地，是本课题的核心挑战。具体可分为三个方面：
　　　（1）软件供应链攻击路径多变，现有软件成分分析技术应对多源依赖和异构复杂软件存在局限性：随着软件供应链的复杂性和异构性不断增加，攻击者可以利用多变的攻击路径，通过供应链中不同来源的依赖库、开源组件、第三方插件等环节发起针对性的攻击。现有的成分分析技术在应对跨平台、跨架构的异构环境时，难以全面覆盖复杂的依赖关系，尤其在检测动态变化的依赖库版本时存在局限，容易遗漏关键风险。因此，急需在源代码和二进制代码两方面研究针对异构复杂软件供应链的软件成分分析技术。
　　　（2）软件供应链威胁暴露面持续增多，现有风险管控与安全测评能力不足：随着软件供应链日益复杂化，应用系统引入了大量外部组件、开源库和第三方服务，导致其威胁暴露面不断扩大。每个外部依赖项、更新或插件都可能成为潜在的攻击入口，使得供应链面临的安全风险大幅增加。现有的风险管控和安全测评手段在面对这种复杂环境时能力不足，导致软件系统的安全性和稳健性难以得到有效保障。如何在自研和外包软件的验收过程中，建立科学的风险评估模型，以确保外包软件的安全性并准确评估其自研率。这涉及开发自动化工具以支持外包软件的安全验收，并优化自研率分析，以减少对外部供应商的依赖风险。
　　　（3）软件供应链底层组件自主可控度低，开源基础架构存在断供风险：在国产化信创环境下，软件供应链底层的关键组件和基础设施大多依赖于国外技术和开源社区，导致自主可控能力较弱。一旦国际环境或供应链发生变化，如技术封锁或开源项目断供，可能会对基础软件组件的持续使用和升级带来严重影响。如何在现货软件采购和硬件设备采购过程中，确保软件和固件的持续安全性。特别是在供应商断供和固件的二进制分析中，需要提升漏洞检测与管理能力，制定有效的风险缓解策略，以保障采购软件和硬件设备的安全。此外，也需探索如何将现有技术与实际开发和采购流程进行集成，实现自动化的安全检测和风险评估支持。确保安全工具能够与开发平台和采购流程无缝对接，减少人工干预，提高自动化检测和修复的效率。此外，通过在军工、金融、运营商、能源、政务、制造业和高校等行业中的应用示范，验证技术的实用性和可靠性，确保所研发的解决方案能够满足不同行业的安全需求。
2.3 拟解决关键技术
　　　1.源代码的代码基因特征快速提取技术研究
　　　针对代码克隆同源检测中的复杂性问题，解决全量代码基因提取效率低、数据冗余以及处理增量代码变更的高成本，实现基于增量代码片段基因标记的快速特征提取技术，应对开源项目高频增量更新和大规模代码库的代码克隆检测场景。
　　　2.适用于二进制代码特征快速提取技术研究
　　　针对信创环境下二进制软件，解决无法获取源码、异构编译影响相似性的问题，实现二进制代码中快速识别安全漏洞函数，应对跨平台、跨架构、编译优化等复杂场景。
　　　3.基于大语言模型开源组件漏洞缺陷提取技术研究
　　　针对开源组件漏洞修复行为的学习，解决自动获取漏洞关联函数的问题，实现基于大语言模型的Java缺陷函数修复行为分析，应对代码漏洞可达性分析及漏洞修复支持的复杂场景。
　　　4.基于深度学习的二进制函数向量相似度模型技术研究
　　　针对信创软件环境下供应商以二进制等复杂交付场景，常见源码分析困难、开源成分不清、漏洞危害难以溯源、法律侵权风险难以鉴别等核心难点，研究二进制软件包多维度分析评估技术，该技术综合利用不同特征的识别方法，并将识别结果汇总，生成一个由不同维度依据组成的检测结果，从而保证高准确率和高检出率。
　　　5.基于文件字节特征的二进制启发式解包技术研究
　　　针对信创环境下多样化二进制软件封装格式，解决传统文件后缀和文件头识别方法不足的问题，实现基于字节特征的启发式解包技术，应对行业专有格式、多重封装和加壳文件的复杂场景。
　　　6.基于源代码漏洞可达性分析技术研究
　　　针对源代码漏洞可达性分析的局限性问题，解决传统软件成分分析无法确定组件真实使用情况、漏洞利用路径及运行时组件加载等问题，实现对组件使用、调用路径及动态引入组件的全面分析，应对依赖版本、操作系统及外部配置等苛刻漏洞利用条件的复杂场景。
　　　7.二进制软件包多维度分析评估技术研究
　　　针对信创软件融合开源软件可能带来的安全漏洞风险，解决二进制软件成分分析和漏洞检测难题，实现基于多维度检测技术的交叉验证，应对多样化运行场景的二进制软件快速特征提取和高效检测。
　　　8.构建海量开源项目监控与源数据采集系统
　　　针对信创复杂场景下海量源代码与二进制代码的监控与采集，解决自动化监控和增量更新采集问题，实现面向多数据源的代码特征基因库构建技术，应对开源组件、供应链软件、闭源软件等数据源的实时监控与高效采集需求。
　　　9.构建分布式海量代码特征基因提取系统
　　　针对海量代码处理，解决代码特征基因提取，实现高效的分布式存储与处理，应对大规模代码分析和特征提取场景。
　　　10.构建基于大语言模型识别开源组件漏洞缺陷提取系统
针对开源组件漏洞修复，解决漏洞函数的自动识别与修复行为学习，实现漏洞修复模式的深度分析与预测，应对漏洞关联函数识别和代码修复优化场景。
2.4 主要创新点
　　1.突破国产软件成分分析产品二进制分析结果低检出率、高误报率的问题。解决信创环境中多处理器、多操作系统、异构编译、编译优化选项等复杂环境二进制软件包成分分析的难题。
　　　2.突破大规模代码库中代码特征提取的效率低的问题，解决人工编写漏洞规则成本高，效率低的问题，实现基于大语言模型识别开源组件漏洞缺陷提取系统自动生成漏洞可达性分析规则和漏洞靶向热修复补丁。
　　　3.首创软件多源异构复杂供应链的风险评估体系，实现风险量化评估，并提供漏洞可达性分析、漏洞靶向热修复等能力为供应链安全治理管控提供支撑。
2.5 技术路线与实施方案
　　　项目将按图 2 所示的“面向源代码与二进制代码的软件成分分析技术研究，异构复杂软件供应链风险评估体系研究，基于信创国产化场景的异构复杂软件供应链风险管控与治理平台，异构复杂软件供应链安全治理的典型应用场景示范验证”四大任务展开。

图 2 项目实施方案和技术路线
2.5.1面向源代码与二进制代码的软件成分分析技术研究
　　　面向源代码与二进制代码的软件成分分析技术需求，本课题围绕异构复杂软件成分分析技术所需的自主可控、安全合规、生态兼容适配、高效可靠和供应链安全等要求，研究代码特征快速提取技术、源代码软件成分分析技术以及二进制代码软件成分分析技术。具体的研究技术路线和技术方案如下图所示。

图 3 研究技术路线和技术方案图
2.5.1.1 代码特征快速提取技术研究

　　　(1)基于增量代码片段基因标记技术的源代码特征快速提取技术研究
　　　在工业界中，代码克隆同源检测的研究已长达几十年之久，经过大量学者不断地研究，取得了不断地进展。一般的，业内将代码克隆定义为四种类型：
　　　类型一（完全相同的代码）：除空格、注释之外，两个代码片段完全相同。
　　　类型二（重命名/参数化代码）：除变量名、类型名、函数名之外完全相同。
　　　类型三（几乎相同的代码）：存在语句增删、使用不同标识符、文字、类型、空格、布局、注释，但依然相似的代码对。
　　　类型四（语义相似的代码）：相同功能的异构代码，在文本或者语法上不相似，但在语义上有相似性。
　　　无论是对于上述四种类型的哪一种，都是按照一个方式将代码片段计算为一段哈希或一段向量。无论是哈希还是向量，在面对全球代码库中的海量代码片段时，能够进行代码片段快速提取以及快速比对，是解决代码克隆同源检测的关键一环。
　　　对代码片段基因库的构建，关键问题在于如何降低在构建代码片段基因库过程中，对代码变更的检测和提取的复杂度。传统构建时都是通过每个开源项目的每一个Commit提交提取一次代码片段基因，对于开源项目发生增量代码的变化时，99%的情况变更的代码范围都不超过总代码数的10%，若对每一个Commit提交都提取一次代码片段基因，则大大增加了工作复杂度，已经难以胜任千万行级别甚至上亿行级别的代码检测任务。此外，代码增加与迭代速度也日益变快，比如开源仓库Github中每天都会有成千上万的贡献者贡献代码，在这样的情况下，如果每次都进行全量的代码克隆检测将花费较高的代价。
　　　如下图所示，本项目针对上述问题，研究了一种用于增量源代码特征快速提取的技术。具体步骤如下：
代码基因标记：对增量代码及其前后相邻的代码片段进行基因标记。
数据记录：记录同一文件内所有基因序列及变更行数的变化量。
增量计算：仅计算并记录增量代码的基因特征及其具体内容，忽略非增量部分的数据存储。
基因对比：在进行代码基因对比时，如果增量哈希的行数变化占总行数的比例超过预设阈值，则直接认定其基因相同；对于不满足该条件的增量哈希，则进一步将增量代码还原并与源代码进行全量哈希对比。

　　图 4 增量代码片段基因标记技术的源代码特征快速提取
　　通过这种方法，可以有效地提高增量源代码特征提取的效率和准确性。从而解决开源项目源代码提取慢、数据冗余的问题，在高频率增量代码的场景下达到开销与性能的平衡。
　　　(2)适用于二进制代码特征快速提取技术研究
　　　信创环境下软件大多以二进制文件形式存在，且基于对软件产权的保护很多软件无法获得源码，当软件所使用的组件出现安全漏洞时，需要能够立刻确认二进制软件包中是否受该组件函数影响。传统检测方法一是通过组件版本和关联漏洞，二是通过提取对应出现安全漏洞函数在二进制代码中的字节签名，在目标二进制中搜索是否包含该函数字节签名。
　　　存在以下情况时，无法检测出软件是否受到威胁：
　　　1.软件开发过程中，仅使用部分函数代码，未使用这个组件。
　　　2.软件编译过程中，编译器自动去除未使用过的函数，其中可能还包含组件版本关联的函数。
　　　3.同一段函数代码，经过不同的编译器、编译参数、操作系统平台以及处理器架构的处理后，可能生成多种二进制变种，从而导致函数字节签名不一致。
　　　在二进制领域，已经有许多方法被提出用于判断两个二进制函数的相似性，以下是几种具有代表性的技术：
1.基于哈希的相似函数比较技术：该方法通过将二进制文件中的函数数据按照一定规则分片，并计算每片数据的哈希值。比较时，通过统计相同哈希值的数量来衡量相似度，相同哈希值数量越多，相似度越高。虽然哈希技术能够以较低成本实现快速查找，但在异构环境下，任何细微的变化都会导致哈希值变化，从而降低准确性。
2.基于指令序列的相似函数比较技术：编译后的函数指令在数据空间中按顺序排列。该技术将指令中的操作码和操作数进行归一化处理，并根据操作码的类别进行标记，进而对比两个函数的相似性。然而，由于跨平台、跨架构等因素导致的差异，指令归一化可能会丢失精确的语义信息，从而影响语义层面的比较，无法涵盖异构场景。
3.基于控制流图的函数相似比较技术：BinDiff 是一个典型的基于控制流图的二进制相似性比较工具。它通过将两个二进制文件中函数的控制流图以图结构进行比较。然而，不同的编译优化等级会对控制流图产生显著影响，导致该方法在跨编译优化等级时表现较差。此外，它对函数语义的支持也较为有限。另一种工具 Diaphora 通过函数、伪代码、汇编代码和调用图等多种特征粒度，结合人工选定的特征来判断函数相似性。然而，由于异构系统导致二进制函数的最终表示存在差异，无法确保所有特征在跨平台和跨架构场景下保持一致，进而影响匹配效果。此外，Diaphora 对每个函数相似性的判断过于依赖人工介入。
4.DeepBindiff利用自然语言处理技术：该技术利用自然语言处理技术来分析汇编指令和控制流图结构，结合基本块和图结构，生成函数的嵌入向量（embedding）。然而，它对函数基本块和控制流结构过于敏感，尤其是在跨架构场景下，只关注低层次特征，导致嵌入的代码片段缺乏解释性，从而降低了对异构环境的支持能力。
　　　针对以上情况，研究适用于二进制代码特征快速提取的技术，尤其是针对信创环境下异构复杂的二进制场景，能够通过提取出的二进制代码特征中的函数信息，识别出其中的安全漏洞函数信息。
　　　(3)基于大语言模型开源组件漏洞缺陷提取技术研究
　　　通过大语言模型和抽象语法树来提取组件漏洞的缺陷函数，主要分漏洞缺陷函数识别和关联函数提取两部分。

　　　　　　　　　　　　图 5 大语言模型漏洞缺陷函数识别方案
　　　(a)大语言模型漏洞缺陷函数识别方案。通过监控GitHub、GitLab等平台的组件项目仓库，捕捉提交中的代码变更内容。确保系统能够获取最新的代码提交记录，尤其是涉及漏洞修复的代码变更。通过抓取提交日志和代码差异内容，建立代码变更数据源，为后续的分析奠定基础。获取到代码提交记录后，通过基于大语言模型的缺陷修复行为模型，对代码变更进行深入分析，利用模型对提交内容的语义进行理解，结合代码注释、提交信息等进行判断，识别出潜在的缺陷修复行为，区分普通代码变更和漏洞修复提交，获取缺陷代码所在函数信息。
　　(b) 漏洞缺陷关联函数识别方案。通过大语言模型识别出的缺陷函数，首先利用AST（抽象语法树）解析其代码结构，重点分析该函数的调用关系，如图：

图 6 漏洞缺陷关联函数识别方案
　　　系统自动遍历所有调用该缺陷函数的其他函数，识别和提取这些关联函数，形成完整的调用链图。这种方式能够快速定位开源组件中可能受到影响的函数，输出组件漏洞风险关系函数列表，为后续的修复和安全评估提供依据。
2.5.1.2 源代码软件成分分析技术研究
　　　(1)基于自监督学习的跨语言代码克隆检测方法研究
　　　A.方法整体结构
　　　该方法主要包括三个部分：
　　　a) 数据预处理。去除与源码无关的信息，利用语法解析工具将代码解析为AST, 并对AST的节点进行上下文信息扩展。
　　　b) 同义词转换。对不同编程语言的AST进行同义词转换处理。通过建立同义词转换表，统一不同语言中的节点类型和节点值，以减少不同编程语言AST之间的差异，确保不同语言的AST可以进行有效比较。
　　　c) 代码克隆检测模型。将处理后的AST输入到基于对比学习的树卷积神经网络中。通过对比学习，模型最小化克隆代码对之间的距离，最大化非克隆代码对之间的距离，从而提高检测的准确性。最终，通过计算代码表示的相似度来判断是否存在跨语言代码克隆。

图 7 基于自监督学习的跨语言代码克隆检测方法整体框架
　　　B.数据预处理
　　　数据预处理是对源代码进行解析和增强处理，以获取源码的语义信息，主要包括以下三个步骤：a) 去除源码中多余的换行符、注释等无关信息；b) 将源码解析AST;  c)扩展AST节点信息，通过对 AST 自顶向下的遍历，将子节点信息添加到父节点中，以获取 AST 的上下文信息。
　　　C.基于字典的同义词转换
　　　基于字典的同义词转换旨在通过标准化不同编程语言中的抽象语法树（AST）节点表示，消减语言间的语法差异。通过构建一个包含不同编程语言节点类型和节点值同义词映射的字典，将具有相同语义的术语统一为标准化术语。在处理AST时，根据该字典将不同语言中的节点类型和节点值进行替换，从而形成具有一致语义的统一AST。此方法有效地减少了由于语言特性差异导致的检测误差，提高了跨语言代码克隆检测的准确性和鲁棒性。
　　　D.代码克隆检测模型
　　　跨语言的代码克隆检测主要分为四层，分别为嵌入层、卷积层、池化层和对比层:
　　　a) 嵌入层
　　　通过将AST节点的类型和节点值转化为向量表示，提供了对代码语法和语义的深层次理解。它通过构建节点的词汇表和嵌入矩阵，将不同编程语言中的节点信息标准化为统一的向量形式，从而减少语言间差异，为后续的卷积层和对比学习层提供了丰富的特征输入。这一过程不仅提升了模型对节点特征的捕捉能力，还支持了跨语言代码克隆检测的准确性和有效性。
　　　b) 卷积层
　　　通过应用树卷积神经网络对AST进行卷积操作，提取节点及其邻接节点的局部特征。卷积层利用预定义的卷积核对AST中的节点特征进行加权求和，整合了节点的局部信息和上下文关系，从而生成全面的特征表示。这些特征向量不仅捕捉了节点的语法结构，还反映了其在整体代码结构中的角色，为后续的池化层提供了丰富的特征输入，提升了模型对代码模式的识别能力。
　　　c) 池化层
　　　通过对卷积层输出的特征向量应用最大池化操作，从而汇总整个AST的局部特征为一个全局特征向量。该池化层通过选取每个特征维度上的最大值来简化特征表示，保留最显著的特征信息，并将其作为后续对比学习层的输入。这一过程有效地减少了特征维度，提升了模型对全局结构特征的捕捉能力，增强了代码克隆检测的准确性和鲁棒性。
　　　d) 对比层
　　利用对比学习的损失函数对AST的向量表示进行优化，通过最小化正样本之间的距离和最大化负样本之间的距离，从而提高模型区分克隆对和非克隆对的能力。该层通过计算向量之间的相似度并应用N-pair损失函数，优化模型对克隆代码的识别精度，并最终将这些优化后的特征用于判断代码对是否存在克隆关系。这种方法提升了模型的泛化能力和检测的准确性。
　　　(2)基于关键阻塞链识别的漏洞修复传播路径分析方法研究
　　　如下图所示，是本研究内容的具体方法模型。

图 8 关键阻塞链识别的漏洞修复传播路径分析
A.监控包管理系统生态系统的演变
　　　为了准确捕捉整个生态系统中漏洞修复的传播情况，该方法持续收集包管理系统包的漏洞元数据和依赖元数据。通过比较两个连续的包管理系统依赖元数据快照，还可以挖掘包的迁移历史，为制定修复策略提供线索。
B.元数据建模与收集
　　　将包管理系统快照的元数据建模为一个 2 元组 M(si) = (dmi, Gi):
是已披露漏洞的集合，其中。字段Idk和 slk表示漏洞µk的唯一标识符和严重级别。字段 pk 和 verk = {v1,v2,··· ,vn}代表受该漏洞影响的包名及其发布的版本。
Gi = (Vi,Ei,Ci)是描述包管理系统快照 si依赖元数据的有向图。Vi表示在快照si上发布的包版本集合。Ei = {pk@va → pu@vb|pk@va,pu@vb ∈ Vi}  表示包版本之间的依赖关系集合。每个依赖关系pk@va → pu@vb ∈ Ei由其对应的依赖约束 c(pk@va,pu) ∈ Ci,决定，其中 c(pk@va,pu)表示pk@va对 pu指定的版本约束，vb是在快照 si上满足该约束的pu的最高版本（符合包管理系统的依赖解析规则）。
　　该方法从三个具有代表性的数据库——GitHub Advisory DB、Snyk Vulnerability DB 和 NPM Security Advisories——爬取上述漏洞元数据。而依赖元数据 Gi则借助包管理系统公共注册表提供的 RESTful API 进行爬取。本方法每周更新元数据模型M(Si)，以持续监控漏洞和包管理系统依赖元数据的演变。为分析漏洞修复在包管理系统生态系统中的传播情况，dmi只记录已通过修复补救的漏洞。
C.挖掘包迁移历史
　　在演化过程中，该方法通过比较两个连续包管理系统快照 si和 si+1 的依赖图 GGG，识别包的迁移记录，从而为制定修复策略提供线索。正式定义包迁移记录如下：
　　定义包迁移记录：如果一个项目弃用包并采用包 作为替代，将定义为迁移记录，其中是源包，而是迁移中的目标包。
　　结合库迁移挖掘方法和收集的依赖元数据，该方法根据两个标准收集包迁移记录：
如果在快照中存在，而在快照中，废弃了包 并依赖于新包，本方法将记录为依赖变化对。如果这样的依赖变化对在不同项目中出现超过 M 次，本方法认为是可能的迁移记录。本方法的默认值为 M = 5，这可以通过实验验证有效地减少噪声。将其自适应调优留待未来工作。
对于每个可能的迁移记录，为了保证其可靠性，本方法使用关键字“replace ” OR “deprecate ” OR “remove ” OR “switch ” OR “migrate ” OR “delete ” 在 GitHub 上搜索其对应的迁移提交。本方法将 确定为某个迁移记录，当且仅当在返回的代码提交中能够识别出新添加的包 。值得注意的是，对于源包，本方法可能会在迁移记录中找到多个目标包。在推导补救策略 C 时，可以为开发人员提供所有可能的建议，以迁移有问题的包。
D.识别关键阻塞链
　　本方法构建的元数据模型 M(si) = (dmi, Gi)包含了识别脆弱路径和阻塞链所需的充分信息。
E.识别脆弱路径
　　设为受  中的漏洞影响的脆弱包版本集合， 为最新的活跃包版本集合（即过去一年发布的版本）。在依赖图上，本方法将包版本视为根节点，将脆弱包版本视为叶节点，通过可达性分析识别所有脆弱路径。注意，本方法仅考虑根节点为最新版本的活跃包的脆弱路径，以分析漏洞对整个生态系统的现状影响。
　　　然而，一个包的版本可能包含多个漏洞，每个漏洞对应于一个特定的安全版本集。例如，Dotty 0.1.0 包含两个漏洞 CVE-2021-23624 [48] 和 CVE-2021-25912。对于这两个漏洞，Dotty 的安全版本分别是 {≥ 0.1.1} 和 {≥ 0.1.2}。为了准确找到阻塞包，在识别漏洞路径时，一个包含多个漏洞的包版本会被区分为多个叶节点。通过这种方式，本方法确保一个漏洞路径涉及一个唯一的漏洞。
F.识别关键阻塞链
　　　对于每个漏洞路径，从漏洞包开始，本方法迭代计算每个包的漏洞 μt 的安全版本，直到找到一个阻塞包  使得。然后，本方法将  视为一个阻塞链。
具体而言，每个包的安全版本是这样确定的：
对于漏洞包，本方法记录其安全版本集为，其中表示在包管理系统快照中发布的版本集合，记录了受 影响的 的漏洞版本。
于漏洞路径上的其他包 ，本方法使用函数 ResolvedSV 来计算其安全版本集。假设 是  在漏洞路径上的直接依赖。ResolvedSV 返回依赖于安全版本 的  的版本，或淘汰包的  的版本。
　　最后，本方法根据通过这些阻塞链的漏洞路径数量对识别出的阻塞链进行排序。排名最高的阻塞链被视为关键链，需要修复以便将漏洞修复传播到大量的包中。
2.5.1.3 二进制代码软件成分分析技术研究
　　　(1)基于文件字节特征的二进制启发式解包技术研究
　　　在当前信创环境中，大多数软件以二进制代码形式发布，针对不同处理器架构、操作系统、定制化需求以及行业领域的差异，二进制软件包的封装格式多样化。现有的二进制解包技术主要面向通用格式，如压缩包、镜像文件等，但对于复杂和多样化的场景（如安装包、固件、数据包、多重封装格式等），传统基于文件后缀和文件头部字节（魔数）的识别方法已经显得力不从心。这种局限性导致文件格式无法被正确识别，进一步阻碍了二进制文件内容的深入分析，尤其是面对行业专有格式、加壳格式及复杂的多重封装文件时，现有技术的处理能力仍显不足。
　　　本项目提出了一种基于文件字节特征的二进制启发式解包技术，旨在通过对不同文件格式字节特征的分析和学习，提升复杂二进制文件的解包能力。其关键步骤如下：
　　　(1)字节特征库的构建：收集各种类型文件的字节特征，建立与文件格式对应的字节特征数据库。该数据库涵盖可执行文件、压缩文件、镜像文件、固件、图片等多种文件格式。通过系统化的特征收集，确保广泛支持常见和行业专有的二进制格式。
　　　(2)启发式分析与标记：研究能够对二进制文件内的字节序列进行深入分析的技术，根据字节特征库中的数据，识别并标记文件格式在二进制文件中的起始和结束位置。基于这些标记，启发式地确定文件格式，即便在文件后缀或头部信息缺失或被混淆的情况下，也能够准确识别文件结构。
　　　(3)深度解包与多重封装处理:对于标记出的文件格式，按照起始和结束位置切割出独立的文件。进一步研究自动化技术，针对加壳、压缩及多重封装格式的文件，进行自动脱壳和解压缩，从而实现二进制文件的深度解包。通过这些处理步骤，能够有效地应对复杂格式、多层封装的二进制文件，提升分析的深度和广度。

图 9 二进制文件启发式解包示意图
　　　如上图是二进制文件启发式解包示意图。这种基于字节特征的启发式解包技术，突破了传统方法的局限，能够应对更加复杂和多样的二进制格式，特别是在行业专有格式和定制化封装格式的场景下，具备更强的灵活性和适应性，为二进制文件的成分分析提供了有效支持。
　　　(2)基于深度学习的二进制函数向量相似度模型技术研究
　　本项目将致力于研究基于多层卷积神经网络（CNN）架构的深度学习AI模型，专注于函数特征语义嵌入的相似性分析。通过将函数基本块中的底层特征、属性控制流程图（CFG）以及基本块之间的数据流转关系融入模型，最终实现每个二进制函数的向量化嵌入，并存储到向量数据库中。该方案的核心优势如下：
　　(1)图结构与孪生网络的结合：项目采用基于图结构的模型训练方法，并引入Triplet Network孪生网络。这一设计能够携带更多函数的语义信息，提高模型对不同场景下函数相似性的鲁棒性，确保在面对多样化输入时，依然能保持较高的准确性与适应性。
　　(2)多维度的信息融合：通过结合函数特征语义、函数基本块、属性控制流以及数据流等多个维度进行信息融合，模型可以更全面地捕捉函数的特征，即使在信创跨平台、跨架构的复杂异构场景下，依然能够保持较高的相似性判断精度。此设计使得模型在多样性和复杂性显著增加的环境中仍具有较强的泛化能力。
　　(3)高效的向量表示与检索：每个函数通过深度学习模型进行向量化表示，使得计算速度大幅提升。通过引入近似最近邻搜索算法 HNSW（Hierarchical Navigable Small World），模型在处理海量函数向量数据时，依然能实现毫秒级响应。这不仅保证了在亿级函数向量数据库中的高效检索，还极大地提升了系统的实用性和扩展性。
　　如下图所示，同一段函数代码，经过不同的编译器、编译参数、操作系统平台以及处理器架构的处理后，可能生成多种二进制变种。对于任意两个变种，借助大型模型的判断，都可以输出其相似性。

图 10 二进制函数代码变种
　　　(3)面向函数内联场景的二进制到源代码函数相似性检测方法研究
　　　二进制到源代码相似性检测方法广泛应用于包括代码搜索，开源软件复用检测,以及软件组成成分分析在内的多个领域。已有的方法通常认为复用函数之间包含了等价的函数语义,因此通常使用一对一的匹配方式来对函数进行比较。
　　　然而，复用函数之间并不一定总是承载着等价的函数语义。当函数内联发生时，一个二进制函数通常由两个或者多个源代码函数编译生成。此时，若二进制函数在编译过程内联了源代码函数，查询函数和目标函数之间的映射关系将会从一对一转变为一对多。函数内联广泛存在于二进制文件中，当使用O3优化级别时，接近40%的二进制函数是通过内联生成的。而现有的二进制到源代码相似性检测方法在处理含内联的二进制函数时，性能损失高达30%。因此急需解决函数内联带来的“一对多”的匹配问题。
　　　针对上述问题,本项目提出了一种采用“一对多”的匹配机制来匹配二进制函数与源代码函数的方法。下图展示了本方法的核心思想，总体而言，传统的“一对一”匹配方法通过比较二进制函数和源代码函数得到复用结果，而本方法旨在生成源代码函数集合，用以补充源代码函数的缺失，从而来与内联的二进制函数进行对比。源代码函数集合是一个由多个源代码函数组成的函数集合，该源代码函数集合包含了该二进制函数对应的源函数以及参与内联的其它源函数，因此其语义等同于内联后的二进制函数。从而帮助现有技术在函数内联的情况下进行二进制到源代码函数相似性的匹配。

图 11 “一对多”匹配机制
　　方法的工作流程如下图所示分为三个部分：特征提取、内联函数调用预测及源代码函数集合生成。

图 12 总体工作流程图
A.特征抽取
　　　通常来讲,编译器一般通过衡量内联某个函数调用的成本和收益来决定是否进行内联.所选特征列于表2中.
表 2 内联函数调用预测预测所选特征
对象	部分	特征
调用函数与被调用函数	函数体	语句总数，
While语句数,
Switch语句数,
Case语句数,
If语句数, 
For语句数, 
返回语句数, 
声明语句数, 
表达式语句数
	函数定义	Inline关键字数量,
Static关键字数量
	函数调用	调用次数,
被调用次数
调用指令	位置	路径长度,
是否位于For循环内,
是否位于While循环内,是否位于Switch语句内,是否位于If条件内
	参数	参数总数量, 
常量参数数量
　　　特征主要来自于两个部分：调用函数与被调用函数,以及调用指令.
　　　调用函数与被调用函数的属性会影响内联被调用函数的成本和收益.成本主要来源于函数内联带来的二进制文件的体积膨胀,而收益主要来源于内联所减少的函数调用开销.本节将从函数体、函数定义及函数调用三个部分出发,介绍本文所选取的用于内联函数调用预测的特征.
　　　在函数体中,本方法提取了不同类型的指令数量作为特征.例如,语句总数表示所有语句的数量,而While语句数表示while循环语句的数量.这些指令的计数代表了函数的体积大小,这表明了内联该函数的成本.简单来说,被调用函数越复杂,内联该函数调用的成本就越高.
　　　在函数定义中,本方法提取了“Inline”和“Static”两个重要关键字的使用次数作为特征.关键字“Inline”是对编译器的建议,表明当其他函数调用此函数时,应将其内联.由“Static”修饰的函数仅可由同一编译单元中的其他函数访问.这些关键字直接或间接地影响编译器的内联决策.
　　　在函数调用中,本方法提取了调用函数与被调用函数的调用次数和被调用次数作为特征.直观上,如果一个被调用函数被众多调用者函数调用,将其内联到所有调用者中的成本会随着调用者数量的增加而增加.相反地,当一个函数仅被调用一次时,内联只会带来优化的好处而不会增加代码大小,这是进行内联的理想情况.
　　　对于调用指令,调用指令的位置和函数调用的参数信息是影响函数内联实施的两个重要因素.例如,如果一个函数调用位于由“for”或“while”定义的循环中,内联该函数调用将显著减少函数调用的时间.此外,如果一个函数调用包含常量参数,并且这个参数能帮助确定被调用函数中的某些分支,那么内联该函数只需要内联对应的分支内容,这也会使内联这些函数的成本有所降低.
B.内联函数调用预测
　　　内联函数调用预测旨在预测开源软件中的内联函数调用,主要包括两个部分：模型训练和模型测试.从流程上来讲,本节首先设计一个分类器并在训练数据集上对其进行训练,然后使用这个分类器来预测测试数据集中的函数调用的标签.
1)模型训练
　　　不同的编译器家族和不同的优化级别会导致不同的内联函数调用.考虑到在每种编译设置下,每个函数调用都将有一个相应的内联标签,本方法将内联函数调用预测问题视为一个多标签分类（Multi-Label Classification, MLC）问题.
　　　考虑到内联决策间的关联性,本方法设计了一个名为ECOCCJ48 (Ensemble of Compiler Optimization based Classifier Chains built with J48)的多标签分类器,它使用二元关联（Binary Relevance）为不同编译器家族预测标签,并使用分类器链(Classifier Chains)为不同优化级别预测标签.
     
（a）编译器级别 					（b）优化选项级别
图 13 ECOCCJ48架构图
　　　如上图(a)所示,编译器级别的分类器为GCC和Clang编译器家族分别设计了对应的优化级别分类器,这两个分类器分别独立训练（二元关联）.由于同一编译器家族的编译器做出类似的内联决策,ECOCCJ48整合了同一编译器家族下的函数调用,标签数量将从32减少到8（2种编译器家族*4种优化级别）.
　　　在每个优化级别分类器中,标签在不同优化级别间存在序列依赖,如图6(b)所示.例如,O2中进行内联函数调用预测的输入是函数调用特征及已为O0和O1预测的标签.考虑到在O0和O1中进行的内联决策通常也会出现在O2中,ECOCCJ48的架构可以利用优化间的内联关联性来产生更准确的预测结果.
　　　此外,本方法还使用集成方法(Ensemble Methods)来增强分类器的性能.在性能方面,集成学习方法要优于单模型学习方法.本方法首先在随机选取的训练数据集上训练基分类器,然后通过聚合基分类器的预测来预测标签.由于基本分类器可以在不同的语料库上进行训练,它们能够捕捉到一些稀有的内联模式.
2)模型预测
　　　在模式测试阶段,给定一个开源软件项目,本方法首先提取所有函数调用并构建其函数调用图（Function Call Graph, FCG）.然后,对于FCG中的每个函数调用,本方法提取相应的特征.将这些特征作为输入到ECOCCJ48中,最后得到每个本方法在所有编译设置下的标签.获取每个函数调用的标签后,本方法创建与8种编译设置相对应的8个FCG.然后,在这些8个FCG中标记内联的函数调用.最后,将得到8个有标签的FCG用于进一步的源代码函数集合生成.
C.源代码函数集合生成
　　　在获得标记的FCG后,需要在其中生成相应的源代码函数集合.下图(a)展示了一个有标签的FCG示例.图中用红色边表示内联调用,蓝色边表示普通调用.红色圆圈代表有内联调用的函数,黑色圆圈代表仅有普通调用的函数.
　　　本方法将源代码函数集合生成的过程转化为包含根节点选择和边扩展的内联函数调用图的生成问题.根节点选择目的在于选择可以作为主函数的源代码函数节点,而边扩展则是将每个内联函数添加至主函数中,以生成对应的源代码函数集合.
  			   		  
（a）有标签的FCG 	  		（b）根节点选取		 	（c）调用边扩展
图 14  源代码函数集合生成示例
1)根节点选择
　　　首先,本文将内联子图定义为由内联调用及其关联函数构成的FCG的子图,只要内联子图中的节点能满足以下两个条件之一,它就可以作为根节点来生成子树.
节点是内联子图的根节点.
节点是非根节点,它有到其他节点的内联调用,并且还有其他节点通过普通调用连接到它.
2)调用边扩展
调用边扩展同样遵循两条规则.
如果调用者和被调者之间只有内联边,则遍历内联子图中根节点可达的所有节点.
如果调用者与被调者之间同时存在内联边和普通边,则生成两种版本的源代码函数集合.一种沿着内联边继续包含后续节点,另一种在遇到普通边时停止.
　　　如果内联子图中存在循环,本方法会记录已遍历的节点并在遇到已记录的节点时停止探索该路径.得到源代码函数集合后,本方法使用Understand进行源代码函数内联,针对每个源代码函数集合生成最终含内联的源代码函数,以作为内联二进制函数匹配的目标.
　　　(4)基于模块化语义匹配的二进制代码第三方库模块检测方法研究
　　随着软件规模和复杂度的快速增长，使用第三方库(TPLs)已经变得越来越流行。现有的大多数方法依赖于句法特征识别第三方库模块，当这些特征发生变化或被对手蓄意隐藏时，这些特征的鲁棒性不强。此外，这些方法通常将每个导入的库建模为一个整体，无法应用于软件仅部分使用库代码段的场景。为了在语义级别检测完全和部分导入的TPLs，本项目将研究利用新的程序模块化技术将程序分解为细粒度的基于功能的模块的框架，通过同时提取模块的语法和语义特征，度量模块间的距离，检测程序中相似库模块的复用。
　　下图显示了方法的工作流程。包含两个阶段，分别是二进制模块化和第三方库（TPL）检测，以从二进制程序中预测 TPL。在第一阶段，提出了一个模块质量指标，该指标基于社区检测算法并结合了程序的特定调整。随后，利用带有启发式偏差的算法，根据该指标将二进制程序分解为多个模块。在第二阶段，通过将程序模块与 TPL 模块进行匹配来执行 TPL 检测。通过提取语法特征、图拓扑特征和函数级别特征，以衡量模块之间的相似性。匹配完成后，还引入了模块和库的重要性评分，以提高库检测的准确性。
A.二进制程序模块化
　　程序模块化技术由两个部分组成：模块质量指标和实际的模块化算法。模块指标旨在衡量将函数分组到簇中所带来的质量提升，而模块化算法则以最大化整体模块质量得分的方式组合这些函数。

图 15 方法整体框架图
1)模块质量评估设计
　　　本方法采用了社区检测质量指标作为基线。随后，结合软件特定的启发式方法对这些指标进行了修改，以适应特定的程序模块化任务。
　　　方法选择了Girvan–Newman 模块化质量 (GN-MQ) 作为基线指标，给定一个已被划分为多个簇的网络，该指标计算同一簇中每对节点之间的连接边，并根据节点度的自适应权重累加这些连接出现的次数。如果同一簇中的节点之间没有连接，则该权重将被赋予负值，从而降低整体质量得分。
　　　除了节点之间的连通性，程序模块还有一些独特的特征，可以用作模块质量指标。函数体积就是其中之一，它由函数中的语句数量来指定。在程序中，体积较大的函数通常执行一些核心功能，而体积较小的函数往往是辅助函数。一个完整且连贯的程序模块将包括一小组大体积函数来执行核心功能，以及一些小体积函数，这些小体积函数在核心组周围提供有用的工具。因此，本文提出了函数体积权重传播算法，以在度量中加入权重调整，从而使其更倾向于完整且连贯的模块。对于具有层次结构的程序，顶层的函数往往通过函数调用控制低层函数的行为。传播算法确保顶层函数会比低层函数获得更多关注，从而将更多的权重分配给顶层函数。
　　　除了调整体积大小外，本方法还将度量标准从测量无向图改为测量有向图，因为函数调用具有方向性（从调用者到被调用者函数）。具体而言，只测量父节点的出度和子节点的入度，以避免其他无关调用边带来的噪声。
2)模块化算法
　　基于提出的模块质量评分，将对程序中的函数进行分组以生成模块。将每个函数视为一个独立的集群，并使用快速展开算法重复地合并两个集群，同时最大化整体质量评分。此外，为了使生成的模块更具直观性，在模块化过程中加入了两个偏置来引导过程。为了提高模块化速度，选择了快速展开的 Louvain 算法，这是一种贪心优化算法，用于指导分组过程。
　　在程序开发过程中，设计用于执行相同任务的函数可能会被放置在一起（例如，在同一个源文件中）。因此，在编译成二进制可执行文件后，这些函数将一个接一个地放置。利用这一启发式方法，方法将局部性偏置引入到模块化算法中。关键思想是，我们希望将彼此接近的函数分组，因为它们更有可能执行相同的任务。为此，每个函数根据其在二进制中的位置序列分配一个索引号。根据单一职责原则，每个方法或模块应具有单一功能，该功能应由其封装。方法希望模块具有有限的入口，以确保其功能单一且封装。因此，在模块化过程中引入了入口偏置指标。在本研究中，模块入口定义为只有其调用函数在模块之外的函数节点。

图 16 第三方库模块检测流程
B.第三方库检测
　　　在对程序和第三方库进行模块化之后，本方法提出了一种相似性测量算法，以基于语法和语义特征匹配模块，并检测程序中的第三方库。上图展示了通过模块匹配进行第三方库检测的流程。
1)模块相似度度量
　　　在模块相似性测量中，使用字符串字面量和常量数字作为语法特征。字符串字面量是最重要的特征，因为它通常具有唯一的值，容易被区分。如果两个模块中的两个函数具有相同的字符串字面量，它们很可能是相同的函数。然而，只有一小部分函数具有字符串字面量。因此，字符串只能帮助我们准确匹配一些函数和模块。与字符串字面量相比，常量的唯一性较低。为了解决这个问题，采用TF-IDF算法，为更具唯一性的常量分配更多的权重。
　　　模块由互相调用的函数组成，形成一个调用图。使用传播图核算法来测量调用图的相似性。该算法试图测量两个图之间的图结构和子图结构的相似性。对于更细粒度的特征，采用了RouAlign中的边嵌入方法来测量拓扑中的边相似性，将特定模块的边嵌入为向量。然后，通过向量搜索来找出图的相似部分，这些特征用于测量模块中函数之间的相似性。由于一个模块由多个函数组成，得分将被汇总以测量模块相似性。
　　　为了计算得分，本方法利用一种先进的二进制函数匹配工具Gemini来生成两个给定函数之间的相似性得分。此外，一个模块可能包含具有不同功能的函数，由于每个模块包含多个函数，逐对计算函数相似性需要时间。因此，将采用基于启发式规则的方法来选择函数对。相似的函数通常使用相同的数据组；或者它们会被相同的指针引用。
2)第三方库检测
　　　方法通过检查目标程序中的模块是否可以与签名TPL数据库中的任何模块匹配来执行第三方库检测。对于目标程序中的每个模块，将其与签名TPL数据库中生成的所有模块进行匹配。根据相似性得分对候选模块进行排名，并选择具有高且可区分的相似性的模块。
　　　为了进一步提高准确性，引入了模块重要性（MI）得分，以选择被认为更重要的模块。在启发式方法中，认为模块的大小越大，模块的重要性就越高。因为较大的模块往往具有更多独特的结构，不容易与其他模块匹配。其次，对于一个库，其重要性应该与参考频率正相关，与库包含的模块数量负相关。一个库被其他二进制文件需要的频率越高，库的模块数量越少，如果库的模块在程序中被检测到，那么它应该越重要。基于这一假设，给予模块的匹配置信度得分。更高的匹配置信度得分表示对模块检测的可信度更高。最后，将相似性得分与匹配置信度结合，输出最终的TPL检测结果。
2.5.2 针对异构复杂软件供应链的风险评估体系技术研究
　　面向异构复杂软件供应链潜在风险量化评估需求，本课题围绕海量异构复杂供应链软件风险评估所需的通用性、高效性、安全性和可靠性等要求，研究多源异构供应链软件威胁建模技术、多维度的风险指标体系构建方法、基于源代码漏洞可达性分析技术以及二进制软件包多维度分析评估技术。具体的研究技术路线和技术方案如图 17所示。

图 17 课题二技术路线图
2.5.2.1 多源异构供应链软件威胁建模技术研究
　　　本课题研究的异构供应链软件数据来源广泛且结构复杂，各个行业不同厂商所使用的许可证标准各不相同且存在违规使用许可证的情况。为应对上述问题，课题首先针对许可证的检测问题提出了一种违规许可证数据检出技术，并针对数据差异化的问题提出了一种灵活的差异校准机制，用于处理不同数据源和结构之间的差异。在数据差异化校准的基础上，构建供应链软件威胁模型。

图 18 多源异构供应链软件威胁建模技术路线图
A)违规许可证检出技术
　　　（1）许可证兼容性建模
　　　由于不同许可真之间的兼容关系复杂，难以提取共性，本课题通过构建有向无环图（DAG）来系统地表示和分析开源软件许可证的兼容性。将每个开源许可证及其版本表示为图中的一个节点（顶点），并通过有向边（edges）连接具备“单向兼容性”的许可证。在包含多个不同许可证的代码时，代码可以在图路径终点的目标许可证下发布。
　　　1）单向兼容性
　　　在许可证V1指向许可证V2的情况下，如果许可证V1与许可证V2兼容，那么在使用了两者的代码时，可以在V2下发布。这意味着V2通常是一个更严格的许可证，确保所有包含V1的代码也满足V2的要求。
　　　2）许可证选择
　　　为了构建兼容性图，方案选择了20种最常用的开源许可证。这些许可证被认为是业内最流行的，并且都经过了开放源代码促进会（OSI）和自由软件基金会（FSF）的批准。这些许可证涵盖了不同的类别，如宽松许可、弱保护性许可和强保护性许可。
　　　3）兼容性分析
　　　通过参考多种资源，包括维基百科、许可证文本、论坛讨论，以及GNU网站上的GPL兼容性指南，对这些许可证的兼容性进行了深入分析。特别地，它分析了当多个许可证组合在一起时，哪些许可条件是可以共存的，哪些组合是冲突的。例如，虽然MIT和BSD许可证看似等效，但由于BSD可能包含更多的限制性条款，最终合并代码需要在更严格的BSD许可证下发布。
　　　4）有向无环图（DAG）的构建
　　　在构建许可证兼容性图时，采用有向无环图的结构。图中的每个顶点代表一个具体的许可证版本，每条边表示许可证之间的兼容性关系。由于是单向兼容，边的方向性保证了从较宽松的许可证向更严格的许可证过渡，而不会回到更宽松的许可条件。例如，公共领域的代码（Public Domain）可以过渡到宽松许可（如MIT许可证），但无法反向转换。
　　　（2）许可证违规检查器
　　　许可证违规检查器旨在自动化地验证许可证文件中的许可证声明，检测可能的违规行为，并提供必要的调整建议。具体包括：1）使用FOSSology许可证分析工具从软件包中提取许可证信息并生成SPDX文件；2）通过Tag to RDF Translator将SPDX文件转换为RDF格式；3）通过Floyd-Warshall算法构建许可证图的传递闭包；4）使用许可证图检查SPDX文件中声明的许可证是否兼容，并提供违规调整建议。
　　B) 灵活差异校准机制
　　　所提出的灵活差异校准机制包括数据标准化、数据融合富集、差异校准及数据归一化。具体如下：
　　　数据标准化：收集的异构供应链软件数据需遵循完整性和规范性原则。通过全面的数据收集与融合富集算法，围绕目标对象构建完整的属性集合，确保对研究对象特性描述的全面性和准确性。同时，对目标对象的命名规则、格式约定、别名机制、数据结构、编码规范、输入输出、注释等方面进行统一规范，以确保校准机制的权威性和一致性。
　　　数据融合富集：处理多模态、多层级数据，包括软件本身的信息、运行环境、操作系统、网络、服务器等，整合不同层次和粒度的数据，确保所有数据指向同一对象，增强数据的完整性和表达能力。
　　　灵活差异校准：针对不同类型和来源的数据，制定差异校准策略和合并算法，确保目标对象的意思表示一致，解决多数据源间的冲突与差异。
　　　数据归一化：为支持后续的算法分析，需要对校准后的数据进行归一化处理。通过特征分析（如最大值、最小值缩放处理），将特征值统一缩放到标准区间，消除不同数据指标之间的量纲差异，确保它们的可比性。
　　C) 供应链软件威胁建模
威胁建模作为一种结构化方法，可以用来识别、量化并应对威胁，利用抽象的方法来帮助思考风险，可以帮助确定软件在整个生命周期中面临的威胁、攻击、漏洞等，从而帮助供方和使用方及时发现风险，制定相应措施，确保软件安全。常见的威胁建模方法有威胁6要素STRIDE、威胁树、攻击图等方法。其中，威胁树通过树形结构描述对资产的各种攻击，用根节点表示最终的攻击目标，逐层细化威胁，直到用叶节点表示具体的攻击方式。建模方法包括创建威胁树、标识威胁、威胁量化、识别主要攻击路径、威胁消减等步骤。
为了定量分析异构复杂软件供应链面临的安全威胁，本课题从攻击者的角度建立软件供应链的威胁树模型，以加强对软件供应链的安全风险管理。软件供应链威胁树模型如下图所示，其中G为根节点，M为中间节点，L为叶节点，各节点的含义如下表所示，其中G代表攻击的最终目标，即软件供应链攻击。G的子节点包括M1-M5的中间节点。

图 19 软件供应链威胁树模型
	其中各节点含义信息如下：
（1）恶意篡改（M1）
表示在软件供应链的设计、开发、生产、集成等环节，对软件产品或组件进行恶意篡改、植入、替换等，以便嵌入包含恶意逻辑的软件或硬件。M1的具体攻击手段包括植入恶意程序（L1）、植入硬件木马（L2）、篡改第三方组件（L3）、未经授权的配置（L4）、篡改供应信息（L5）等叶节点。
（2）假冒伪劣（M2）
表示软件产品或组件存在侵犯知识产权、质量低劣等问题，具体包括不合格产品（L6）、未经授权的生产（L7）、假冒产品（L8）等叶节点。
（3）供应中断（M3）
表示由于自然或人为因素造成的软件产品供应量或质量的下降，甚至导致软件供应链中断，包括突发事件中断（L9）、基础设施中断（L10）、不正当竞争（L11）、组件停止生产（L12）等叶节点。
（4）信息泄露（M4）
表示软件供应链上传递的敏感信息、关键数据等非法泄露，包括共享信息泄露（L13）、商业秘密泄露（L14）、信息窃取（L15）等叶节点。
（5）违规操作（M5）
表示软件供方的违规操作行为，包括违规收集和使用用户数据（L16）、滥用大数据分析（L17）、影响市场秩序（L18）等叶节点。
为了分析叶节点攻击成功的概率，定义Pm为父节点实现的概率，、、…、为其下各子节点的概率，各子节点之间为OR关系，即任意一个或多个子节点攻击成功都代表其上的父节点攻击成功，且有：
 
即父节点攻击成功的概率为其下各子节点攻击成功概率中的最大值。
对于每个叶节点定义安全评价指标，即安全属性A，且，其中C为攻击成本，D为攻击难度，F为攻击被发现的可能性。为了定量分析叶节点的风险，定义叶节点攻击实现的概率为：
　　　
其中、、分别表示攻击成本C、攻击难度D和攻击被发现可能性F的权值，且有；分别表示3个安全属性的效用值，且对应与C、D、F成反比关系，为了便于计算，本课题取。
首先需要采用专家打分的方式对每个叶节点的3个安全属性进行评估，具体评价标准见下表。为保证打分的客观性和专业性，需要选择不同行业或者同一行业内不同领域的3-5位专家进行打分。
表 3 安全属性评价指标

在打分过程中为避免受到个人意识、认知等主观因素的影响，采用模糊层次分析法（FAHP）计算安全属性的权值。FAHP是利用元素之间的逐一比较结果构成模糊一致矩阵，再计算各元素相对重要性权重的方法。
基于FAHP方法计算元素相对重要性权重的方法是：参照尺度表对元素之间两两比较后构造出模糊判断矩阵C，如果C满足公式1，则C是模糊一致矩阵；如果不满足公式1，需要按照公式2转换成模糊一致矩阵，最后利用式公式3求出各元素相对重要性的权值。
公式1：
公式2：
公式3：
基于以上方法对威胁树中每个叶节点进行威胁量化，完成供应链威胁建模。
2.5.2.2 多维度风险指标体系构建方法研究
本课题从技术、管理、知识产权、可维护性和闭源组件等多个维度对异构复杂软件供应链的安全风险进行全面评估，并构建了相应的风险评估指标体系，如下图所示。在具体评估过程中，本课题依据软件组件的来源（如开源、闭源）选择相应的评估指标。针对闭源组件信息缺乏、有效评估指标不足的特点，综合代码特征与二进制代码成分分析结果，额外设计二进制代码维度下的评估指标，进行针对性的风险评估，以确保评估结果的准确性和全面性。

图 20 多维度风险评估指标体系图
　　A）软件技术评估维度
技术类指标以软件生命周期为主线，从软件设计、编码、构建、测试、发布和运维6个阶段进行评估。
（1）设计阶段
该阶段主要进行软件组件选取和软件组件设计，软件组件设计可通过软件组件技术文档体现，在各软件组件中具体评估。软件组件选取主要从软件组件的来源、许可信息、所属社区运营状态、是否有替代产品以及能否实现自主演化等方面考察软件组件的可溯性与可用性。
　　　（2）编码阶段
在各软件组件的编码阶段，因为内部开发团队不同而存在一定差异，不便统一评估，所以在评估结构组成风险时，对单个软件组件编码阶段的风险进行单独评估。
　　　（3）构建阶段
　　　该阶段共分为两步，一是将源码编译构建生成可安装运行的软件组件，二是将相关软件组件进行版本构建生成镜像文件。软件组件构建工具（如debuild、pbuilder等）和版本构建工具（如pungi、OBS等）在构建过程中可能影响软件内容，存在植入和篡改风险。在构建时，配置文件中的开发人员、版本号、许可证和依赖关系等信息是否详尽准确，这将影响产品的可溯性，可通过构建工具和配置文件来评估构建阶段的风险。
　　　（4）测试阶段
　　　根据测试方案，本项目使用测试工具对软件组件进行功能性和安全性测试。测试阶段发现潜在风险的能力取决于测试方案的完备性和测试工具的性能。然而，测试工具也可能对软件内容产生影响，存在植入或篡改的风险。为评估测试阶段的风险，需要关注测试方案的完备性、测试工具的性能以及安全性。
　　　（5）发布阶段
　　　通过特定渠道将系统镜像和软件组件发布，并交付给用户。如果发布渠道受到攻击，发布的软件内容可能存在被植入或篡改的风险，在发布时进行正确性验证可以在一定程度上降低这些风险。为评估发布阶段的风险，需要关注发布渠道的安全性及发布时的正确性验证手段。
（6）运维阶段
该阶段需要及时修复发现的安全风险并发布升级补丁，主要包括以下3个环节：首先，及时发现并收集安全风险信息；然后，快速响应并及时修复；最后，通过升级渠道将补丁部署到用户端。软件安全受安全风险信息收集的及时性和全面性、响应速度以及升级渠道安全性的影响。本课题主要通过风险信息收集和升级渠道安全性两个指标来评估运维阶段的风险。
B）软件管理评估维度
　　　在评估软件组件的运营风险时，需要具体分析其结构组成风险。组织的运营风险会从整体上影响软件供应链的安全性，一般通过组织背景判断组织的持续经营能力，根据市场管理评估产品的竞争力。在评估可溯性风险、知识产权风险和安全风险时，不仅要关注技术方面的风险因素，还需要考虑管理方面的风险因素。这些风险可以通过供应链管理、知识产权管理和安全管理来进行评估。
（1）组织背景
组织背景在评估组织持续运营能力时起着关键作用，主要考虑核心团队、盈利能力和技术积累时间3个方面。稳定的核心团队、较强的盈利能力以及较长的技术积累时间通常意味着组织可持续运营能力较强，从而降低了运营风险。
（2）安全管理
安全管理主要从以下3个方面评估软件的安全保障能力：安全管理组织、安全开发流程和安全事件响应策略。安全管理组织的建立决定了组织内是否有专责人员关注并负责安全事务；安全开发流程确保了产品开发过程中的安全性；安全事件响应策略是指安全事件发生后采取的措施，可以有效降低安全事件对软件造成的影响。
（3）供应链管理
供应链管理主要从供应方管理、配置管理和工具管理3个方面评估人和组织、代码和组件以及工具的可溯性与可用性。供应方管理关注是否掌握向软件提供内容的开源社区和内部开发者的情况；配置管理主要看是否掌握源代码和软件组件的版本迭代以及供应方的相关信息；工具管理主要评估是否掌握系统生命周期过程中使用的各种工具及其来源，同时跟踪掌握其当前可用状态。
（4）产权管理
产权管理主要从知识产权管理组织、知识产权管理制度、知识产权纠纷应对3个方面评估组织对知识产权管理的情况。知识产权管理组织关注是否有专业人员对产品的知识产权进行风险管理；知识产权管理制度评估是否制定了完善的知识产权管理规章制度，是组织进行知识产权管理的依据；知识产权纠纷应对通过审查组织在历史知识产权纠纷事件中的表现，了解组织在知识产权管理方面的实际能力。
（5）市场管理
市场管理主要从产品市场占有率、支持的指令集架构、应用软件生态3个方面评估产品的市场竞争力。市场占有率能直接反映软件的市场竞争力，支持的指令集架构和应用软件生态则是通过软件上下游的生态间接判断软件的市场竞争力。
C）软件知识产权评估维度
知识产权类指标主要从知识产权风险以及许可证限制风险两个方面评估软件产品在专利、著作权、许可证中存在的风险及限制。
（1）知识产权风险
知识产权风险是指在软件开发和使用过程中，可能面临的侵犯他人知识产权的法律风险。这类风险一旦发生，可能会导致软件无法继续使用或面临巨额赔偿。
1）代码侵权风险：需要对软件项目中的代码来源进行审核，确保没有侵犯他人的版权。特别是在引用第三方库或模块时，应当明确其授权协议是否允许当前的使用方式。
2）第三方贡献的合法性：对于开源社区中的外部贡献者提交的代码，需进行合法性审查，确保不会因引入第三方贡献而引发知识产权纠纷。
（2）许可证限制风险
开源软件通常会使用各种不同的许可证，这些许可证对于软件的使用、修改、发布等行为都有明确的限制。如果不仔细遵守这些限制，可能会引发法律纠纷，甚至导致软件的商业化运作受阻。
1）许可证兼容性：在使用多个开源软件或库时，需要评估它们的许可证之间是否兼容。例如，GPL许可证对软件的发布有较严格的要求，而Apache许可证则相对宽松，混合使用时需确保不违反许可证规定。
2）许可证合规性：确保在发布或商业化软件时，遵守所使用开源软件的许可证条款。例如，按GPL要求公开源代码，或根据MIT许可证注明原作者信息等。
3）许可证的延展性：评估软件许可证是否允许未来的商业化运作，以及是否对软件的扩展和变更有任何限制。例如，某些许可证可能限制了软件的二次分发或修改后的再发布。
D）软件可维护性评估维度
传统软件可维护性评价指标主要从易分析性、易修改性、模块性和易测试性等软件属性出发。本课题结合开源软件供应链规模化、社交化的发展特点，主要从以下几个指标进行评估：
（1）团队健康
团队健康性评估主要关注软件开发和维护团队的规模与协同能力。一个健康的团队通常由多个角色组成，具备良好的协作机制和较大的团队规模，这意味着在核心贡献者离职或流失的情况下，其他成员仍能够持续维护和更新软件，确保项目不会因人员变动而停滞不前。这种抗风险能力是软件长期稳定性和可持续性的关键指标。
（2）软件活跃度
软件活跃度评估侧重于软件在开发、维护和讨论等方面的动态表现，反映了其在特定时间段内的活跃水平。评估内容主要涵盖以下三个要素：
1）社区活跃度：社区活跃度是评估软件生存力与发展潜力的重要指标，反映了用户和开发者对软件的关注程度。活跃的社区意味着用户群体持续关注并积极贡献，这有助于推动软件的健康发展并提升其竞争力。相反，社区活跃度的下降可能预示着用户流失，进而影响软件的生命力。同时，社区活跃度也从侧面体现了软件的质量与影响力，只有高质量的软件才能吸引并留住更多用户和贡献者。
2）依赖平均更新时间：依赖库的更新频率是衡量软件维护性和安全性的重要指标。使用过时版本的依赖库会增加运维风险，而依赖库的频繁更新则表明软件在维护上较为积极，且具备较强的风险控制能力。因此，依赖的平均更新时间可以反映出软件在版本维护上的活跃度与安全性。
3）发布频率：发布频率是软件活跃度评估的关键因素之一。高频次的版本发布表明开发团队在持续改进软件并及时引入新功能和修复问题。每次新版本的发布都是软件发展进程中的一个重要里程碑，展示了项目的进展。相对较高的发布频率通常意味着开发团队对软件的积极维护与迭代，这对用户体验和软件的长期发展都具有重要意义。
（3）依赖影响力
依赖影响力是指在开源软件供应链中，有多少其他软件直接或间接依赖于当前软件。软件被广泛依赖通常意味着其功能稳定、质量可靠，已经通过了大量用户和场景的验证。广泛的依赖性不仅表明该软件满足了用户需求，也为其提供了更多的使用反馈，推动其不断改进和优化。因此，依赖影响力大的软件，往往具有较好的维护性和健全的风险应对机制，降低了可维护性风险的发生概率。
（4）外部依赖度
与依赖影响力相反，外部依赖度指的是一个软件对外部模块的依赖程度。通过复用外部代码，依赖项中的所有漏洞和缺陷都会通过依赖关系传递到软件自身，因为软件完全依赖这些外部模块。如果一个软件没有任何外部依赖项，那么其外部依赖度为0。这样的软件在维护上相对简单，因为维护人员只需专注于程序自身逻辑的实现，而不必担心外部依赖项可能带来的安全风险或技术债务。
（5）软件生态
软件生态是一个复杂的社会-技术系统，其环境可以是软件公司、研究组织，或者虚拟的开源或开放开发社区。开源软件基金通常通过多种方式支持开源软件的发展，如推广和宣传、提供开源项目指导、以及为软件开发和相关活动提供资金补助等。商业支持同样为软件的可持续发展提供了稳定的资金来源，保证了各种开发和维护活动的正常进行。
E）闭源二进制代码风险评估维度
异构复杂软件供应链中包含大量的闭源二进制代码，如何对这些来源信息不充分以及源码信息不足的闭源组件进行有效评估是本课题研究的重要一环。基于课题一与课题三中提供的二进制代码特征提取与成分分析结果，本课题围绕闭源二进制代码风险评估维度进一步提出相应的评估指标，具体如下：
（1）组件安全性评估
利用安全分析工具扫描二进制代码中的已知漏洞或恶意代码签名。分析工具应能够识别常见的安全漏洞，如缓冲区溢出、未初始化的内存使用、以及不安全的函数调用。将闭源二进制代码特征与已知漏洞数据库（如CVE数据库）进行比对，量化代码中已知漏洞的数量和严重程度。结合漏洞数量和安全更新频率，使用加权算法计算综合安全风险评分。
（2）自研率评估
　　基于二进制代码特征相似性比对，识别外部代码的比例。计算自研代码占总代码的百分比。自研代码比例较低，意味着外部供应链组件的使用比例较高，可能增加软件的潜在风险。
（3）依赖性评估
　　评估闭源二进制组件对外部库和服务的依赖程度，分析其可能带来的连锁风险。使用成分分析工具扫描闭源二进制代码，识别出使用的第三方库和服务。分析依赖库的层级关系和调用频率，评估核心组件对外部依赖的敏感性。根据依赖库的历史漏洞、维护状态、许可证合规性等指标，量化依赖库的风险。
（4）动态行为评估
　　通过运行时监测评估闭源二进制代码的实际行为，检测潜在的动态风险，如未预料的网络连接、权限提升等。将闭源二进制组件在受控的沙箱环境中运行，监测其行为，包括系统调用、网络活动、文件访问等。将运行时行为与正常操作模式对比，识别异常行为，如尝试连接未知IP地址、未经授权的文件修改等。评估组件在运行时的CPU、内存、磁盘等资源的占用情况，识别异常的资源消耗模式。计算运行期间检测到的异常行为数量并基于资源使用率计算性能消耗评分，结合行为异常次数和资源消耗评分，量化动态行为的风险。
（5）性能与效率评估
评估闭源二进制组件在实际运行环境中的性能表现，识别低效或资源浪费的部分。通过标准化的性能基准测试工具，评估组件在CPU、内存、IO等方面的性能表现。在模拟的高负载环境下运行组件，观察其在极端条件下的表现，包括响应时间、吞吐量、稳定性等，分析组件的稳定性（如崩溃次数、响应延迟）并量化得分。
F）基于多维度指标的量化评估
为了实现软件供应链安全风险的量化分析，本课题采用层次分析法（Analytic Hierarchy Process，AHP）对多维度评估指标进行综合评估，以确保评估结果的科学性和可靠性。该方法能够处理复杂的层次问题，并为不同维度指标分配合理的权重，从而实现对供应链安全风险的精确量化。
　　　层次分析法确定权重的主要步骤为：1）根据同级指标之间的相对重要性比值，构造判断矩阵；2）为确保权重分配合理，应对判断矩阵进行一致性检验，若不符合一致性条件，需调整判断矩阵；3）求出判断矩阵的最大特征根对应的特征向量，并进行归一化，得到本级各指标的相对权重。
本课题综合考虑不同来源的组件与不同维度之间的关联性，筛选合适的指标维度对组件进行特性化评估，并量化评估结果，以此衡量组件在特定维度下的供应链安全风险。
2.5.2.3 基于源代码漏洞可达性分析技术研究
　　　传统的软件成分分析技术通常首先识别出项目中引入的组件，然后进一步检测这些组件中潜在的漏洞，并将此类数据作为检测结果。然而，这种技术存在以下缺陷：
　　　1.无法判断组件是否被实际使用：开发人员可能引入了某个开源组件，但并未在代码中实际引用或调用它。
　　　2.无法识别组件的利用路径：即使开发人员在项目中使用了某个开源组件的代码，如果该代码没有被直接调用，或系统中不存在调用路径，漏洞也无法被利用。
　　　3.漏洞利用条件复杂：某些漏洞的触发需要依赖特定的条件，如特定版本的JDK、操作系统或外部配置等。
　　由于上述问题，检测到的组件漏洞并不一定具备被利用或触发的可能性。
　　A) 建设漏洞可达性规则模板
　　由于每个组件的漏洞多种多样，且组件的使用方式、漏洞的利用手段以及修复方法各不相同，本课题计划借助大语言模型，对组件修复代码、漏洞PoC/Exp以及漏洞原理进行深入分析。从中提取出与漏洞相关的关键代码调用点（Sink点函数）和可能触发漏洞的特定配置项，进而生成一系列精准的检测规则。这些规则将有助于识别漏洞的真实利用路径和潜在风险点。
　　如下图所示是漏洞可达性分析规则模型示例图

图 21 漏洞可达性分析规则模型
　　B) 漏洞可达性验证
　　为了解决传统漏洞检测中的不足，本课题将研究并采用漏洞可达性验证技术。通过对项目源代码进行抽象语法树（AST）分析和配置文件的解析，结合预先生成的漏洞可达性规则，验证是否存在漏洞的可达路径或配置缺陷。如果确认存在实际的调用路径或配置问题导致漏洞可达，则该漏洞具备被真实利用的可能性，需优先修复。通过这种方式，可以有效降低漏洞误报率，提高漏洞检测的准确性和修复的优先级。
2.5.2.4 二进制软件包多维度分析评估技术研究
　　　信创软件融合开源软件的同时，也潜藏着开源安全漏洞风险，可能直接或间接引入开源软件，或在开源软件的基础上二次封装，导致开源安全的专业性与复杂性难以评估，开源软件引入的安全漏洞依赖关系、影响范围、破坏程度等信息不明确，将给信创软件及厂商带来无法预估的风险。
　　　信创软件的交付形式通常以二进制格式的软件成品及其配套设施为主，且信创软件的运行环境具有多样化特征。因此，对二进制形式的软件成品进行软件成分分析和漏洞检测成为当前工作的重中之重。本课题旨在针对信创软件多样化的运行场景，满足二进制软件快速特征提取和高效检测的需求。通过结合多维度的检测技术，本课题将实现交叉验证，从而提升检测的准确性和全面性。
　　本课题将针对信创多样化的场景环境，满足二进制软件快速特征提取与快速检测的需求，利用多种不同维度的检测技术进行交叉验证的检测技术，研究一种二进制软件包多维度分析评估技术，从二进制函数、函数调用关系、二进制字符串三个维度，多维度分析二进制中的软件成分组成，并进行交叉验证，提高二进制检测的准确度。
　　以下是检测技术原理。用作成分检测的二进制文件称为“原始二进制文件”，检测出的组件称为“来源二进制组件”。
　　　（1）基于深度学习的二进制函数相似度模型检测技术
　　尽管学术界和工业界在二进制函数相似性比较技术方面取得了显著进展，但当前的二进制函数级别检测技术仍然存在一些局限性，特别是在处理异构环境时，效果有待提升。函数级别的组成成分分析技术，并未有将此技术落地在软件供应链场景的具体应用。本项目将利用课题1中研究的“基于深度学习的二进制函数向量相似度模型技术”，实现这一技术的应用。
　　首先，建立一个保护海量函数特征向量的数据库。将二进制组件的函数通过模型嵌入，得到函数的向量特征，再将向量特征存入向量数据库，并建立函数向量特征与组件的对应关系。对海量向量通过聚类将向量空间划分为多个子空间（或称为“桶”），每个子空间包含一组相似向量。查询时，首先找到包含查询向量最可能相似向量的几个子空间，然后在这些子空间内部进行精确搜索；并对数据进行逻辑上的分片和分区，便于管理和分布式处理。
　　其次，通过模型获取原始二进制文件中的全部函数向量，利用缓存机制存储热门查询的结果或部分索引结构，以减少磁盘I/O和加速查询响应，结合标量过滤条件，进行预计算以优化查询流程，使用近似最近邻搜索算法 HNSW将原始二进制文件的函数与向量数据库中的函数进行搜索匹配。
　　最后，得到相似度最高的的多对相似函数，通过该深度学习模型匹配到的任意一对函数，我们称之为“相似函数对”。若在原始二进制文件与来源二进制组件的函数匹配结果中，存在大量或高比例的相似函数对，可以检测出原始二进制中包含该来源二进制组件；若数量过少过比例过低，则需与其他检测技术相结合。
　　　（2）基于图匹配的函数调用关系检测技术
　　　对于“相似函数对”中的相似函数所来自的组件，称为“来源二进制组件”。基于检测函数在原始二进制文件中的函数调用关系，可以构建“检测函数调用关系图”；同样，基于相似函数在来源二进制组件中的函数调用关系，可以构建“相似函数调用关系图”。
　　　
图 22 检测函数调用关系图
　　　如上图所示，在检测函数调用关系图中的任意一个被调用的函数，都能够通过深度学习模型，在相似函数调用关系图中找到其对应的相似函数。即在以A1和A2为起点的两个调用关系图中，存在路径对应关系：如在检测函数调用关系图中有{A1 -> C1}（A1调用C1），那么在相似函数调用关系图中则存在对应路径{A2 -> C2}（A2调用C2），且{A1,A2}是相似函数对，同时{C1,C2}是相似函数对，这一对应关系我们称之为“相似调用路径”，对于存在一定数量相似调用路径的，可以检测出原始二进制中包含该来源二进制组件。
　　　（3）基于二进制字符串分词特征的检测技术
　　　首先，对原始二进制文件中的函数符号、导入导出符号、ASCII字符串、UNICODE字符等字符级数据进行分词，并建立索引数据库。每个字符串都经过预处理以提取词干，随后对分词结果建立词汇表和倒排索引。基于此，创建了字符串倒排索引数据库，并建立了来源组件与字符串、字符串与倒排索引之间的实体关系。如下图所示，展示了索引生成及权重设定的过程。

　　　图 23 索引生成及权重设定过程
　　　根据每一个分词的价值进行权重设定，如下是部分权重价值表：
表 4 部分权重价值表
　　　类型	　　　权重	　　　示例
　　　版本号/电话/人名/...	　　　高	　　　V1.3.8
　　　Web协议/域名/邮箱/...	　　　中	　　　https://xxxxx.com
　　　单词/数字/...	　　　低	　　　hello
　　　通过在倒排索引数据库中搜索原始二进制文件中的每个字符串，并计算所有分词权重的总和，权重最高的字符串所对应的来源二进制组件即可被检测为原始二进制文件中包含的来源二进制组件。
2.5.3 构建异构复杂软件供应链分析及风险评估平台技术研究
　　　面向解决异构复杂软件供应链数据源的实时监控、高效采集和快速提取，以及应对漏洞关联函数识别和代码修复优化场景需求，本课题围绕构建异构复杂软件供应链分析及风险评估平台、构建海量开源项目监控与源数据采集系统以及构建基于大语言模型识别开源组件漏洞缺陷提取系统，依托现有平台技术，增强软件成分分析平台能力以及软件风险评估与管控能力。具体的研究技术路线和技术方案如下图所示。


图 24 技术路线和技术方案
2.5.3.1构建海量开源项目监控与源数据采集系统
　　在构建面向信创复杂场景的代码特征基因库平台技术研究中，对海量的都源代码与二进制代码进行监控采集是目前急需完善的技术领域。本项目将针对来自多种数据源的海量源代码与二进制代码进行监控与采集。
　　如下图是海量开源项目监控与源数据采集系统架构图。

图 25 海量开源项目监控与源数据采集系统架构图
　　通过自动化监控技术，对开源组件、信创供应链软件、开源软件、开源代码库、闭源软件等软件项目与数据源进行持续监控。对每个数据源进行识别，明确需要采集的数据源；对数据源中的项目进行元数据收集，包括其项目信息、项目结构等，确定其监控范围；并通过对代码仓库的监控，实时获取最新的代码提交记录，实时校验二进制软件包哈希值是否一致，确定其是否发生增量更新；若发生增量更新，则触发自动化采集。
　　对存储库中不存在的代码进行全量采集。当触发增量更新时，对更新的代码或软件包进行增量采集，并通过对增量代码进行提取合并，经过数据清洗与校验后存入存储库。
2.5.3.2构建分布式海量代码特征基因提取系统

图 26 分布式海量代码特征基因提取系统的架构图
　　上图展示的是一个分布式海量代码特征基因提取系统的架构图，主要分为两大部分：
　　1.存储库模块
存储库：图中展示一个数据存储库，存储了大量代码资源。存储库可以包含各种代码，如源代码和二进制代码。
获取代码：从存储库中提取代码的步骤。获取代码后，进入下一步进行调度和执行。
分布式调度系统：负责对从存储库获取的代码进行分布式调度，确保系统能够处理大规模的代码，并通过执行模块将代码输入到后续的处理步骤。
　　2.基因提取模块
代码输入：获取的代码进入该模块，进行基因提取。
二进制代码判定：有一个判定步骤判断输入的代码是否为二进制代码。如果是二进制代码，则执行二进制反汇编，将其转化为汇编或中间语言代码，以便进一步处理。
代码预处理：对于非二进制代码，系统会对代码进行预处理，去除语言无关的特征，比如代码行数、注释比例、模块依赖关系等。此步骤有助于标准化代码，使其更易于进行特征提取。
代码特征快速提取技术：在预处理后的代码中，系统利用快速提取技术提取出代码中的重要特征，生成代码的片段基因。该步骤是整个系统的核心，负责生成特征数据。
特征索引：提取的代码基因被索引，以便后续的检索与查询。
分布式基因库：最后，所有提取出的代码片段基因存储在分布式的基因库中，确保高效的存储和后续的查询。
　　本系统通过分布式调度和特征提取技术，能够高效处理海量代码并提取代码片段的核心基因。
2.5.3.3构建基于大语言模型识别开源组件漏洞缺陷提取系统
　　通过对开源组件漏洞代码修复的前后变化学习，构建漏洞缺陷函数的大语言分析模型，能够自动获取组件漏洞的关联函数，可以为代码分析中的组件漏洞可达和漏洞的修复提供有效的支撑。
　　　
图 27 开源组件漏洞缺陷提取系统
　　　(a)漏洞关联组件项目代码仓库设计实现。通过使用NVD、GitHub Security Advisories等API接口，定期自动化采集CVE漏洞数据，比对公开漏洞关系库（osv等），精准筛选与Java相关的漏洞。利用NLP（自然语言处理）模型，深入解析漏洞公告、修复提交记录和版本发布日志，智能化关联CVE与开源组件项目仓库。该方案可以有效减少人工干预，提升数据获取和关联的精度。
　　　(b)提取并分析漏洞修复前后的代码差异。支持大语言模型的修复行为学习，利用git diff工具提取漏洞修复前后的代码变化，同时结合AST（抽象语法树）分析，捕捉代码层次结构、函数调用关系、变量变化等关键信息。结合控制流与数据流分析技术，全面了解修复动作的上下文。该方案不仅聚焦代码的直接差异，还通过语义分析理解修复的逻辑和意图。
　　　(c)大模型学习及修复行为模型构建。对大量已修复的Java漏洞修复数据进行训练，构建具备代码语义理解能力的深度学习模型。模型将学习漏洞修复的常见模式和策略，形成一套可以识别新漏洞并预测修复行为的知识库。该方案将采用增量学习机制，定期更新模型，以确保模型能跟随最新漏洞修复行为不断提升识别能力。

2.5.3.4软件成分分析平台能力增强技术
　　为满足当前异构复杂的供应链分析需求，针对不同系统及不同类型的分析对象，在现有产品能力基础上，引入源码高阶克隆检测技术、二进制软件包多维度分析评估技术以及基于深度学习模型的二进制检测技术。同时，面对海量的供应链基因数据，运用大数据分析技术增强分析效能。智能化的数据聚合与模式识别提高了风险捕捉的准确性与全面性，从而显著增强平台在复杂环境中的响应能力与分析效能，优化供应链安全管理和风险识别。
(1)增加高阶克隆检测实现自研率分析、二进制检测能力
　　基于包管理的源码检测分析，能够有效解决在源代码项目中存在包管理器文件（如Maven的POM文件等）时，针对第三方组件依赖和许可证进行分析的问题。通过Hash技术对文件和代码片段进行分析，可以识别等值内容的成分，从而基本满足软件开发中引入的开源组件或开源代码带来的风险管理需求。然而，对于异构复杂供应链中所面对的软件，这些方法仍存在局限，难以进行有效分析。通过引入最新的研究成果，可以解决无法解压、误报漏报等一系列问题，构建一个能够支持异构复杂软件供应链分析需求的治理平台。

　　图 28 检测能力演进图
　　
高效的模拟构建分析。通过模拟软件编译过程，能够在脱离原编译器环境的情况下高效分析第三方组件的引入。此方法不仅大幅提升了分析效率，还增强了处理异常问题的能力，减少了对特定构建环境的依赖。
语义化代码检出。通过高阶源码克隆检测技术，系统能够实现语义级别的片段比对，精准识别语义相同但结构不同的代码片段，并具备非标准开源许可的检测能力，提升了代码相似性分析的广度和深度。
二进制文件分析。通过二进制函数相似度匹配和字符串特征分词技术，系统能够对除源码外软件包进行有效分析，结合大数据模型和AI技术，确保检测结果的准确性和全面性。
(2)处理能力增强：利用基于大数据的海量代码基因分析对比技
　　基于大数据的海量代码基因分析，带来了海量特征数据的存储和检索问题，通过大数据基础来实现对海量数据存续检索对比能力。如图，通过大数据技术来实现分析对比。

图 29 分析比对流程图
多任务多线程分析技术。在大规模代码基因特征提取过程中，多任务多线程技术能够显著提高分析的效率。基因特征提取需要对大量的源码和二进制文件进行并行分析，以识别出特定的代码片段和函数的相似度。通过多任务多线程技术，可以将大数据集拆分成多个子任务，每个线程独立执行一部分数据的分析，从而充分利用系统的计算资源，缩短整体分析时间。具体实现流程包括：
任务分解：将需要处理的分析对象按照一定规则划分为多个子任务，比如按文件、代码块或函数级别进行拆分。多线程并行处理：通过线程池或并行框架，为每个子任务分配独立线程进行特征提取。每个线程对代码或二进制文件进行语法、语义或相似度特征的分析。
结果汇总：各线程完成子任务后，将提取到的基因特征数据汇总到主线程，进行统一的结果分析与优化，最终形成全局特征库或用于比对的基因图谱。
通过多任务多线程的并行处理，不仅提升了特征提取的速度，也增强了对海量数据的处理能力，有效应对大规模供应链代码基因的复杂分析需求。
大数据模型与存储。在海量基因数据的背景下，大数据存储和检索技术尤为重要。根据基因数据的不同类型和检索时的业务特性，采用专门的存储模型，来提高海量数据的检索能力：
二进制基因数据：采用向量存储引擎（如 Milvus）来处理大规模的矢量数据，以便于快速查找和比对。
结构化元数据：通过 MongoDB 存储代码的结构化信息，如组件依赖、版本、许可等。
代码片段基因：通过 Elasticsearch (ES) 实现全文检索，对海量的文本型基因片段进行快速匹配和查询。
HDFS 分布式文件系统：用于存储海量的原始数据、分析结果及历史记录，以支持大规模数据的持久化。
2.5.3.5软件风险评估与管控能力增强
(1)软件安全风险量化评估
　　基础的产品具备了组件路径溯源、组件漏洞识别风险治理，通过组件漏洞可达性分析和二进制软件包多维度分析将大大增强平台的安全风险评估能力。
组件漏洞可达性分析。软件供应链分析及风险评估采用软件成分分析工具进行开源组件及其漏洞分析，报告中大量的组件漏洞被检出，但是否可以被验证、可以被利用成为一大问题，通过漏洞可达性判断自研代码到漏洞是否存在可达路径，从而判断漏洞是否会被自研代码触发分析来检测潜在的漏洞和安全风险。

图 30 检出效果图

二进制软件包多维度分析评估。二进制文件分析其复杂底层代码结构，单一维度评估导致结果的高误报率、低置信度，通过多维度分析技术的引入，将分析维度覆盖到组件版本号匹配、函数向量匹配、字符串分词特征匹配、函数调用关系匹配、导入导出符号匹配，解决异构复杂（跨CPU架构、操作系统、编译器等）环境下无法精确对比二进制函数问题，并填补信创环境成分分析领域二进制函数级分析技术的空白。

(1)运行时漏洞靶向热修复技术
　　在静态源码组件风险的管控，已通过组件依赖修复和安全版本推荐实施了相应的风险处理方案。然而，对于已经上线的软件或者缺乏对应修复组件版本的情况，目前面临着重大挑战。此时，运行时组件漏洞靶向修复能够有效弥补这一技术空白，并且具备低侵入、精准修复的强大能力。部署示意图如下:

图 30 靶向修复部署示意图
　　对于已知的正在运行的 Java 应用组件漏洞 ，Java Agent 的 JAR 文件中含有在程序启动时或运行时被调用的 premain 方法或 agentmain 方法，在这些方法中加载插件，利用类加载器加载插件 JAR 文件并获取其中的类和方法，找打对应组件漏洞的sink点和需要修复的函数字节码位置，再通过反射机制调用修复函数并将其注入到正在运行的程序中。流程图如下：


　　图 32 靶向修复流程图
　　一旦插件被加载，Java Agent 能够在运行时拦截对漏洞 sink 点的方法调用，并将其重定向到修复函数，修复函数执行相应的修复操作以消除漏洞。
2.5.4 开展异构复杂软件供应链安全治理的示范应用
　　本课题通过示范应用验证异构复杂软件供应链安全治理方案的有效性和适用性，旨在建立一种适用于广泛行业的风险评估和安全治理框架。随着5G、物联网、云计算等新兴技术的快速发展，运营商行业不仅支撑着全国范围内的通信网络，还承载着海量的敏感数据和关键的通信服务，其中大量应用系统也逐步迁移至国产化开发运营环境中，运营商的网络架构和服务面临着前所未有的复杂性和安全挑战。在这一背景下，运营商行业能够覆盖大部分信创应用场景的验证需求，其涵盖的业务应用场景如软件自研、外包开发、现货采购和硬件设备采购具有广泛的代表性和复杂性。这些场景不仅涉及到网络设备、通信协议和数据处理等多个方面，还需要应对来自外部威胁和内部漏洞的双重挑战。
　　本课题计划在运营商行业进行重点应用示范，通过在运营商行业进行示范应用，可以有效检验和优化技术解决方案的综合能力，同时为其他行业提供有力的参考和借鉴，后基于成熟验证后的软件供应链安全治理体系，再覆盖至军工、金融、能源、政务、制造业、高校等关键信息行业。本课题的示范应用基于不同行业的共性及差异性，主要在以下四个场景中进行开展：
　　（1）软件自研开发场景
　　在软件自研开发场景中，各行业都需要在开发流程中嵌入高效的安全检测和风险评估机制，以确保软件在开发阶段能够识别和修复潜在的安全漏洞，从而保障最终产品的安全性和可靠性。为了实现这一目标，必须对现有平台进行优化和集成，以适应不同技术栈和开发需求。
　　首先，明确各行业对安全检测的具体要求。例如，金融行业对交易系统的高安全性需求，军工行业对数据安全的严格标准等。通过深入分析这些需求，制定出相应的安全检测标准和流程。其次，优化现有技术平台，并将其与开发环境进行集成。包括调整自动化安全检测工具的配置，以适应不同的编程语言、开发框架和应用场景。此外，针对特定行业的技术栈，优化平台的集成方式，实现工具和技术的无缝对接，从而提高整体开发效率和安全性。通过集成，确保软件供应链安全评估能力能够在软件开发的各个阶段进行有效的安全检测。最后，建立实时反馈机制，将安全检测结果及时传递给开发人员，并提供详细的修复建议。通过平台集成，实现检测结果自动化地流入开发环境，并结合开发工具提供即时反馈和修复建议。
　　通过这些措施，可以在软件开发过程中实现高效的安全管理，减少潜在的安全风险，提高系统的整体安全性和可靠性。同时，通过对平台的优化和集成，将提升开发流程的整体效率，确保开发人员能够快速响应和解决安全问题。
　　（2）软件外包开发场景
　　在软件外包开发场景中，各行业在验收外包软件时都必须进行详细的风险评估，以确保所接受的软件符合预定的安全标准，并准确评估其自研率。这一过程需要建立科学的风险评估模型，构建面向外包开发验收过程的软件供应链风险评估系统，并开发自动化工具来生成验收报告。首先，针对外包软件的特定需求，开发并优化风险评估模型，评估外包软件的安全性，识别可能存在的漏洞和风险。其次，设计自动化验收工具，简化外包软件的验收流程，自动生成验收报告，提供详细的风险评估结果。此外，实施自研率分析，准确区分外包组件和自研组件，评估其对整体软件安全的影响，确保外包软件的安全性和合规性，减少对供应商的依赖风险，提升软件的整体质量和安全性。
　　（3）现货软件采购场景
　　在现货软件采购场景中，各行业在采购现货软件时需要特别关注软件的漏洞检测和供应链风险管理，以确保所采购的软件在供应商断供的情况下仍能保持安全性。首先，基于研究的软件安全漏洞检测技术，针对现货软件中的潜在漏洞进行全面扫描和分析。例如，金融行业可能需要针对交易软件的安全性进行深入检测，而能源行业可能关注控制系统的软件稳定性。其次，基于供应链风险评估体系，评估现货软件在供应商断供或其他供应链中断情况下的风险，制定相应的应对策略。开发安全修复补丁管理系统，跟踪并验证补丁的应用情况，确保软件在长时间运行中能够保持高安全性。此外，生成详细的评估报告，为决策提供依据，确保软件的安全性和可持续性，帮助各行业有效管理现货软件的安全风险，保障业务的连续性和稳定性。
　　（4）硬件设备采购场景
在硬件设备采购场景中，各行业需要对采购的硬件设备进行内置固件的二进制分析和风险评估，以识别和解决潜在的安全漏洞。首先，基于研究二进制代码分析技术，对不同硬件设备的固件进行深入分析，识别潜在的安全漏洞和风险。其次，打造基于固件二进制的软件供应链风险检测系统，将固件二进制分析能力集成到现有的验收流程中，确保其能够在实际硬件环境中进行有效的安全评估。此外，进行实际硬件设备的集成测试，验证分析工具在不同硬件环境中的适应性和有效性。开发固件风险评估模型，结合固件分析结果，提供定制化的风险评估和缓解建议，确保硬件设备在实际使用中的安全性，帮助各行业确保硬件设备的固件安全，提升设备的整体可靠性和安全性。


三、项目预期目标
　　本项目总体目标是完成面向信创国产化场景的异构复杂软件供应链分析及风险评估平台的研究与开发。具体内容如下图所示，针对信创国产化场景下的异构复杂软件供应链风险评估与安全治理需求，重点研究面向源代码与二进制代码的软件成分分析技术，突破代码特征快速提取、软件包成分精准识别、软件潜在风险量化评估等关键技术，构建针对异构复杂软件供应链的风险评估体系，打造异构复杂软件供应链分析及风险评估平台，开展异构复杂软件供应链安全治理的示范应用，支撑我国关键基础行业软件的安全与风险评估。

　　图 33 项目目标示意图

3.1 主要技术经济指标
　　项目执行期内，使用本项目成果的产品销售收入3000元，利润1950万元，上缴税金80万元。项目结束后5年内，预计通过产品销售和技术服务，实现销售收入2亿元，利润1.1亿元，上缴税金500万元。
3.1.1 技术指标
(1)完成异构复杂软件供应链风险评估与安全治理研究工作，主要研究内容:针对信创国场景下的异构复杂软件供应链风险评估与安全治理需求，重点研究面向源代码与二进制代码的软件成分分析技术，构建针对异构复杂软件供应链的风险评估体系，开展异构复杂软件供应链安全治理的示范应用;突破代码特征快速提取、软件包成分精准识别、关键技术，构建异构复杂软件供应链分析及风险评估平台，支撑我国关键基础行业软件的安全与风险评估。
(2)申请国家发明专利11项，软件著作权11项，发表论文2项。
(3)研发的异构复杂软件供应链分析及风险评估平台，覆盖不少于四种编程语言(C、C++、Java、JavaScript)异构复杂软件的源代码级成分分析的准确率达到85%以上二进制代码级成分分析的准确率达到75%以上，软件自研率、洞检查、许可证合规等风险分析准确度达到90%以上。此外在实施期内承诺额外完成研发的异构复杂软件供应链分析及风险评估平台如下指标：
①支持基于开发语言包管理器的分析技术，漏洞检查、许可证合规等风险分析，覆盖不少10种编程语言(如：Go,C#,Ruby,Python,鸿蒙仓颉等)；
②支持识别分析.exe、.msi、.bin、RAR等二进制文件格式或压缩格式类型不少于50种；
③支持基于信创处理厂商龙芯、兆芯、海光、鲲鹏、飞腾，非信创厂商MIPS、SPARC、POWER、Intel、AMD等不少于10种类型处理器编译的二进制文件分析；
④建立面向信创异构复杂环境(跨处理器、跨编译器、跨OS平台)软件基因数据库，二进制函数特征向量不少于2千万条，二进制符号特征向量不少5亿条、二进制函数调用关系图不少于600万条；
⑤建立基于大语言模型开源组件漏洞的靶向修复补丁生成与缺陷函数特征提取系统，生成不少于1000条的漏洞靶向修复或漏洞可达性分析规则；
⑥实现支持java应用软件开源组件漏洞靶向热修复技术，兼容不少于3种国产信创中间件；
⑦实现支持基于源代码漏洞可达性分析功能，支持不少于500个CVE漏洞；
⑧支持软件供应链投毒检测,投毒情报数据不少于1万条；
⑨支持分析和导出不少于3种SBOM（软件物料清单）标准格式；
⑩平台系统信创国产化兼容，完成不少于3种国产信创操作系统兼容性认证、1种国产信创数据库、2种国产信创中间件间兼容性认证；
(4)完成国防军工、金融、运营商、能源、政务、制造业、高校等不少于7个行业的应用示范。
　　
3.1.2 经济指标
　　项目执行期内，使用本项目成果的产品销售收入3000元，利润1950万元，上缴税金300万元。项目结束后5年内，预计通过产品销售和技术服务，实现
销售收入6亿元，利润3.3亿元，上缴税金6000万元。
3.2 社会效益
(1)推动技术创新与科研发展
　　本项目的研究将推动多项关键技术的创新与突破，如源代码和二进制代码的成分分析、供应链风险评估体系的构建、以及漏洞特征提取和风险治理能力的提升。这些技术成果不仅将提高我国在软件供应链安全领域的技术储备，还将为相关科研机构和企业提供新的研究方向和应用场景，进一步促进我国信息安全领域的科技创新。
(2)提升相关产业链供应链的安全性
　　随着全球信息化和数字化进程的加快，软件供应链的安全性已成为国家安全的重要组成部分。尤其是在关键信息基础设施领域，软件供应链安全的保障至关重要。本项目通过针对信创国产化场景构建异构复杂软件供应链的分析和风险评估平台，能够有效识别和治理潜在的安全风险，提升国产化软件的安全性，防范因软件供应链漏洞引发的安全事件，从而增强我国在全球数字安全领域的竞争力和话语权；
(3)支持产业自主可控与转型升级
　　本项目将促进我国自主可控信息技术产业的发展，减少对国外技术和产品的依赖。通过自主研发和优化关键技术，如代码特征提取、二进制分析和基于大语言模型的漏洞特征提取等，项目有助于提升国内软件供应链的技术水平和竞争力。同时，项目成果将示范应用于以国防军工、金融、运营商、能源、政务、企业、高校为代表的关键基础设施领域，为这些事关国计民生的重要行业的安全运行提供强有力的安全保障，在推动各行业在信息化和智能化转型升级过程中，保障产业链自主可控、安全可靠。
3.3 技术应用
　　本项目通过构建异构复杂软件供应链分析及风险评估治理平台，实现对信创国产化场景下软件供应链的全方位安全管控。项目将重点应用源代码与二进制代码的软件成分分析技术，结合大语言模型的漏洞特征提取能力，识别和量化软件中的潜在安全风险。此外，平台打造基于固件的二进制分析、自研率分析等能力，并针对不同行业的特定需求进行功能优化与扩展，满足在软件自研开发、软件外包开发、现货软件采购、硬件设备采购等场景，并重点在运营商行业进行应用示范，并推广至军工、金融、政务、能源、制造业、高校等关键信息行业。通过平台的应用，能够有效提高软件供应链的安全性和风险评估的准确性，从而保障国产化软件在各行业应用中的安全性、可靠性和自主可控性，为我国关键信息基础设施和重要行业的数字化转型提供强有力的技术支撑。
3.4 产业化前景
　　在中美博弈以及俄乌冲突的时代背景下，任何无法保证自主可控的关键领域都可能成为制裁重点。国家高度重视信息通信技术安全自主可控，已将信创产业纳入国家战略，启动了信息技术应用创新（以下简称“信创”）工程，信创工作主要实现国家信息通信基础软硬件、应用软件、网络安全装备等国产化自主可控产品研制和应用，解决核心信息技术“卡脖子”问题。然而，自主可控不等于绝对安全，近年来信创环境下供应链导致的安全问题频发，为国产化信创替代工作带来了巨大安全风险挑战。党的二十大报告中强调“着力提升产业链供应链韧性和安全水平”，软件供应链安全既是供应链安全的重要组成部分，又是网络信息安全的基础。软件供应链安全风险不但会直接影响关键基础设施和数字经济安全，甚至会对国家安全产生威胁。
　　国内在软件供应链安全保护方面的工作也在扎实推进中，一是顶层规范不断完善，国家标准中《软件供应链安全要求》和《软件产品开源代码安全评价方法》已发布，《软件物料清单数据格式》也已完成公开征求意见；二是行业建设如火如荼，多个行业的机构开展了面向软件供应链安全、开源软件治理、软件物料清单生成和应用等方面的能力建设、能力评估、工具评价等工作；三是解决方案持续落地，国内一些头部网络安全公司和大型企业组织陆续推出或落地了较为全面的软件供应链安全解决方案，并在持续完善。本项目的研究可解决软件供应链安全领域的痛点和难点问题，因此具备极高的产业化前景。
　　项目承担单位、参与单位2和参与单位5深耕软件供应链安全领域，具备大量的技术基础和能力储备，并在金融、能源、运营商、政务、医疗等多个行业进行过软件供应链安全领域的项目实施落地经验，布局并建设了一套覆盖软件供应链全流程的产品工具链及配套解决方案，能够为项目成果的应用示范和后期应用推广提供充分保障。
　　
3.5 获取自主知识产权
　　本项目实施期内（2025年1月-2026年12月），申请获得自主知识产权如下。
(1)授权本项目相关国家技术发明专利不少于 11项
(2)授权本项目相关软件著作权不少于 11项
(3)发表论文 2项
(4)构建针对异构复杂软件供应链的风险评估体系1项
　　　
四、计划进度安排和课题分解
4.1 计划进度安排
　　　本项目整体工作进度安排如下。
　　　（1）第一阶段（2025.01-2025.03）：方案整体设计、技术调研，完成代码特征快速提取、软件包成分精准识别等关键技术研究及突破，完成面向源代码与二进制代码的软件成分分析技术研究。
　　　（2）第二阶段（2025.04-2025.06）：完成软件风险量化评估等关键技术研究与突破，研发针对异构复杂软件供应链的风险评估体系。完成平台调研、系统设计报告和技术研发计划，提出并优化详细的研发方案和技术细节。
　　　（3）第三阶段（2025.07-2025.09）：完成海量开源项目监控与源数据采集系统、分布式海量代码特征基因提取系统的原型研制，实现第一个里程碑。
　　　（4）第四阶段（2025.10-2025.12）：完成异构复杂软件供应链分析及风险评估平台原型研发，实现第二个里程碑。
　　　（5）第五阶段（2026.01-2026.03）：完成在运营商行业开展异构复杂软件供应链安全治理的示范应用，并基于运营商场景完成技术验证和改进优化，并完成对国产化软硬件场景的适配兼容。
　　　（6）第六阶段（2026.04-2026.06）：并基于运营商场景完成技术验证和改进优化，并完成对国产化软硬件场景的适配兼容，形成其他行业集成试点方案模板。
　　　（7）第六阶段（2026.07-2026.12）：完成在国防军工、金融、能源、政务、企业、高校等行业潜在单位的示范应用，并根据用户反馈进行平台和技术优化。完成项目验收。
　　　
4.2 课题分解
4.2.1 组织方式
　　　项目坚持任务有所侧重、人员按需分配、共同研究攻关的原则管理，按照研究内容分为总体协调组、需求调研组、理论与技术攻关组、产品研发与验证组等四组。各组任务描述如下：
　　　总体协调组：由项目组长与参与单位主要负责人组成，负责项目实施进程中的监督与协调工作，定期审核各课题组长关于课题进展执行情况的书面报告，发现项目进行中可能存在的潜在问题，及时做出决策、协调解决重大问题和突发事件及人员安排。项目负责人监督项目进展，利用先进的项目管理方法指导项目开发，制定项目开发规范及标准，调度项目组资源，解决在工作层次不能达成一致意见的问题，以保证项目成功。
　　　需求调研组：完成项目的背景、目标、应用等各项需求分析，对项目各项文档及功能说明书进行审核和确认，并参与系统测试。
　　　理论与技术攻关组：适用于源代码的代码克隆同源检测代码特征快速提取、适用于二进制代码特征快速提取、基于源代码修复补丁的特征快速提取、基于源代码漏洞可达性分析、基于大数据的海量代码基因分析对比、基于深度学习的二进制函数向量相似度模型、二进制软件包多维度分析评估、基于大语言模型识别开源组件漏洞缺陷提取、基于文件字节特征的二进制启发式解包等技术理论进行研究、对相关关键技术进行攻关等。
　　　产品研发与验证组：保持与其他项目组的紧密联系，负责系统构建、实现及产品化等工作，选择具有代表性的国防军工、金融、能源、运营商、政务、企业、高校等行业领域完成平台测试验证与应用示范。
4.2.2 项目管理机制
　　　首先，为了指导、规范和监督各个子任务的研究和实施，协调各个子任务之间的关系，需成立在项目组长领导下、由各个参研单位的资深专家和课题负责人组成的总体组。总体组每隔半年实地检查各个课题的研究实施情况及资金使用情况，各个课题组每半年向总体组提交年度报告和资金使用报告。总体组根据每个课题的年度报告总结项目的总年度报告和任务调整报告并提交项目组长，项目组长根据各个课题的任务执行情况适时调整各课题的研究内容和方案，协调课题间的协作关系，确保项目的顺利实施。总体组对项目实施中出现的重大问题，负责进行协调，及时提出问题解决方案，并督促相关单位及时解决。
　　　其次，由项目承担单位组织、抽调总体组专家组成技术委员会，对各课题以半年为单位进行周期性考核、严把质量关，滚动式经费支持，确保项目质量与进度。课题负责人向技术委员会每半年呈交一次技术进展情况报告，技术委员会协调各课题间的技术交流需求，半年组织一次讨论会，促进技术交流，适时掌握并调整研究方向。
　　　另外，课题内部各参研单位每月向课题负责人汇报任务进度，组织每季度课题组内部会议，形成会议纪要并存档。课题单位负责对研究过程中产生的技术文件（如论文、专利、技术报告、考察报告、程序源代码、调试与排错过程、测试方案、验收鉴定报告等）建立技术档案，保证系统研发过程的可追溯性。
　　　本项目涉及工业企业、科研院所等多方共同参与。任务分配时将发挥各自优势，产学研结合，提高科研创新的效率和速度，最终将科研成果成功推向市场、推广应用。
4.2.3 课题分解
　　　项目共由6家单位联合研发攻关，项目申请单位负责组织项目申请，立项后组织项目实施、中期及验收等。五家项目参与单位参与项目申请，立项后参与项目实施、中期及验收。
　　　　项目分解为4个课题如下：
　　　　（1）面向源代码与二进制代码的软件成分分析技术；
　　　　（2）针对异构复杂软件供应链的风险评估体系；
　　　　（3）构建异构复杂软件供应链分析及风险评估平台；
　　　　（4）异构复杂软件供应链安全治理的示范应用；
　　　　具体分工如下表所示：
课题设置	主责单位	参与单位
课题1：面向源代码与二进制代码的软件成分分析技术	项目参与单位1	项目承担单位
课题2：针对异构复杂软件供应链的风险评估体系	项目参与单位3	项目承担单位
课题3：构建异构复杂软件供应链分析及风险评估平台	项目承担单位	项目参与单位4
课题4：异构复杂软件供应链安全治理的示范应用	项目承担单位2	项目参与单位5
表 5 课程项目任务分工表




五、现有工作基础和条件
5.1项目申请单位情况

图 34 项目申请单位情况
　　　项目承担单位：****公司专注为用户提供软件供应链安全创新技术的研发和解决方案，是国内软件供应链安全领域的领航者，是一家国家高新技术企业、浙江省专精特新中小企业、浙江省高新技术企业研究开发中心、杭州市企业高新技术研发中心及浙江省科技型中小企业等能力认定。公司拥有国家信息安全测评中心颁发的安全风险评估、安全工程资质，通信网络安全服务能力风险评估资质，国家信息安全漏洞库CNNVD技术支撑单位，CNNVD漏洞信息共享合作单位，信创政务产品安全漏洞库（CITIVD）技术支撑单位，浙江省网络与信息安全信息通报中心技术支撑合作单位，国家计算机网络应急技术处理协调中心浙江分中心支撑单位，浙江省委网信办年度网络安全技术支撑单位，杭州市委网信办年度网络安全技术服务单位，信息技术应用创新工作委员会副组长单位等网络安全领域重要资质。
　　　公司研发总部位于杭州，在北京、广州分别设有分公司，目前拥有技术发明专利25项，软件著作权55项，公司始终坚持科技创新的发展路线，研发人员占总人数60%以上，平均年度研发投入占营业收入总额35%以上，本科以及硕士学历占比90%以上。公司核心团队成员毕业于浙江大学、吉林大学等国内双一流高等院校计算机科学专业，由来自原各大安全上市企业的资深安全技术专家组成，并具有高级工程师职称、注册信息安全专业人员（CISP）等能力认证。公司核心团队曾编写《软件供应链安全实践指南》，由电子工业出版社正式发行出版，并受到中国工程院院士沈昌祥等联合力荐。公司曾主导参与国家标准《信息安全技术 软件产品开源代码安全评价方法》，公安部第三研究所、中国信息通信研究院《静态应用安全测试系统安全技术要求与测试评价方法》《开源软件安全成分分析检测系统安全技术要求与测试评价方法》《动态应用安全测试系统安全技术要求与测试评价方法》《供应链产品供应链安全技术要求与测试评价方法》等行业标准，以及主导参与浙江省地方安全技术标准《基于安全检测插件的Web应用系统安全检测技术规范》编写。
　　　公司软件供应链安全领域的创新技术和解决方案曾荣获2022年度工信部等十二部委网络安全技术应用试点示范项目、IDC 中国DevSecOps技术创新者、2023全国网络安全创新成果大赛优秀奖（开源软件安全分析系统）、2023全国网络安全技术创新成就奖、2024网信自主创新“尖锋奖”、浙江省经信厅浙江省先进（未来）技术创新成果奖、浙江省软件核心竞争力企业、中国信通院软件供应链优秀成果“自主研发创新成果”、2023数字安全创新能力百强、2024中国网络安全新势力30强、2024中国数字安全新质百强、国家工业信息安全发展研究中心网络安全高成长性企业-勇创之星等荣誉，公司同时连续多年入围《CCSIP中国网络安全行业全景册》《嘶吼网络安全产业图谱》《中国网络安全市场全景图》《中国数字安全能力图谱》等软件供应链领域专业榜单图谱。
　　　公司以科技创新助力国家数字软件供应链安全，以“不是需要更多的安全软件、而是需要更安全的软件”为安全发展理念。通过基于AI模型和卷积神经网络，自主研发了全链路智能动态污点分析、二进制函数级智能基因检测与自动化验证等核心技术与产品，为用户提供DevSecOps安全开发解决方案、软件供应链安全一体化解决方案、上线即安全与免疫防御解决方案、基于SBOM开源软件供应链安全情报与治理、软件供应链安全安全检查评估工具等。目前已覆盖各大关键基础设施行业的TOP级用户，包括中国证监会、兴业银行、浙商银行、广东农信社、浙江农信社、河北农信社、杭州银行、北京银行、河北银行、国家进出口保险、中国移动、中国电信、中国联通、国家电网及各省市级大数据发展管理局等TOP级用户，同时也服务了亚运会软件供应链安全检查、关键基础设施用户软件供应链安全专项检查等技术支撑工作。

　　　参与单位1：作为****大学与杭州市萧山区共同建设的政军产学研用金协同发展的产教融合创新综合体，落地浙江以来，研究院获批省级院士工作站、博士后工作站、新型研发机构，以及杭州市****智能汽车电子概念验证中心、工业数据要素流通与安全治理浙江省工程研究中心等重大创新平台，现有在院师生2000余人，其中全职科研人员170余人，双聘导师360余人，预计到2026年底全职科研人员规模达到500人。建立以来获批“浙江省新型研发机构”、“全省模拟集成电路重点实验室”、“工业数据要素流通与安全治理浙江省工程研究中心”、“杭州市****智能汽车电子概念验证中心”、“浙江省博士后工作站”、“院士工作站”、“科技部人才创新驱动中心”等高能级平台。
　　　****是国家“优势学科创新平台”项目和“211工程”项目重点建设高校之一。2017年学校信息与通信工程、计算机科学与技术入选国家“双一流”建设学科。全国第四轮一级学科评估结果中，3个学科获评A类：电子科学与技术学科评估结果为A+档，并列全国第1；信息与通信工程学科位于A；计算机科学与技术学科评估结果为A-档，学校电子信息类学科继续保持国内领先水平。根据ESI公布数据，学校工程学和计算机科学均位列全球排名前1‰。项目申请人所在学院入选全国首批一流网络安全学院建设示范项目（2021年中央网信办和教育部联合结题验收评估获地方院校第一名），建设有国家一流网络安全示范学院，拥有网络空间安全领域唯一的教育部创新团队、民口唯一的密码学国家重点学科、综合业务网理论及关键技术（ISN）国家重点实验室信息安全中心（评估“优秀”）、大数据安全教育部工程研究中心、陕西省网络与系统安全重点实验室（评估“优秀”并推荐申报国家重点实验室）、****-腾讯智慧安全研究院等科研平台。在网络安全领域，先后获国家科学技术奖励3项、省部级一等奖8项。承担了国家重点研发计划、重大专项、自然科学基金重点项目等重要的国家级科研项目10余项，同科研院所、企业开展了大量的横向科研合作。授权国内外技术发明专利100余件，制定网络安全国际标准5项。
　　　参与单位2：****于2014年3月在杭州成立，是其集团贯彻“网络强国”战略和“互联网+”行动计划、促进企业转型发展的重要布局。目前注册资本17.5亿元人民币。
　　　公司坚持党建引领，落实集团公司“力量大厦”总体战略部署和“一二二五”战略实施思路，立足“智慧家庭运营中心”主建职责，聚焦“拓规模，树品牌，建生态，提价值”主要任务，锚定“科技创新先行者、信息服务领航者、数智运营践行者”发展定位，秉持“深耕数智家庭创新能力，让网络更智能，让生活更多彩”愿景，以“全千兆”为引领，不断创新演进“一根线、一块屏、一双眼、一个家、一公里”的“五个一”家庭信息服务体系（HDICT），不断深化基于规模的价值经营和基于场景的客户运营，推进****家庭市场高质量可持续发展。
　　　公司发展基础日益坚实。落实人才强企战略，队伍数字化转型不断深化，公司员工突破2000人，硕士以上学历占比超70%，数字化人才占比超90%。主动融入科创布局，创新条件不断完善，公司具备国家高新技术企业、CMMI-5、ISO14001、信息系统集成资质三级等资质。强化能力沉淀，高质量成果不断形成，深耕以智能“连接、控制、交互、安全”为核心的家庭物联网能力体系，公司在ITU首个发布智能通信、家庭监控国际标准，在ISO首个立项家庭物联网国际标准，在智标委指导下首个立项智慧社区规范，累积制定国际国内标准60多项，累计已授权专利超400件。
　　　参与单位3：***大学是一所综合性的浙江省属重点大学，始建于1897年，现已有10个全国(国家)重点实验室，119个本科专业，62个一级学科硕士、博士学位授权点。在职教职工9557人，教师中有中国科学院院士27人、中国工程院院士21人、文科资深教授14人、高层次人才及优秀青年人才1800余人。在国家公布的“双一流”建设名单中，入选一流大学建设高校（A类）。在第二轮“双一流”建设中，**大学共有21个学科入选一流建设学科。
　　　参与单位xx隶属于**大学**学院，**国家一级重点学科，2017年被入选国家“双一流”建设学科，并在教育部近两轮一级学科评估中获评为“A+”学科，建有**国家重点实验室、**国家工程研究中心、**国家工程实验室、**国家级国际联合研究中心，以及**省部共建协同创新中心，是教育部“**创新引智基地”和“国家自然科学基金创新群体”的依托单位，拥有一支高水平、稳定的学术研究队伍，现有专任教师100余人，其中中国工程院院士1人、国家级高层次人才12人次、浙江省特级专家3人、浙江大学求是特聘教授12人、国家级青年人才20人次、新世纪百千万人才工程国家级人选5人、教育部跨世纪新世纪优秀人才6人，获国家科技进步一等奖1 项、二等奖6项，国家技术发明二等奖1 项。
　　　申报人所在**所团队成立于2021年，由**院士担任学术委员会主任，xx教授担任所长，长期致力于装备自动控制系统软硬件技术、人工智能与工业智能、软件与系统安全等领域研究，取得了**装置及系统、**系统设计开发平台等系统性创新成果。目前为止承担了23项国家级重大攻关项目，包括国家重点研发计划项目3项/课题7项、工信部工业互联网发展专项2项、国家重大基础设施专项1项、国家自然科学基金重大/重点项目10项等，攻克并转化一批关键核心技术，形成核心技术体系和重大产品、装备，凝练和产出一批标志性科研成果，成果达到国际先进与国际领先水平，获国家科技进步一等奖1项，二等奖4项，省部级一等奖5项，CAA科技进步特等奖1项，为申报人提供了良好的研究基础和平台。
　　　参与单位4：****研究院是按照“人才培养、科技创新、产业发展、国际交流”四位一体的建设思路，由浙江省、杭州市、滨江区三级政府共建的高能级新型研发机构，是杭州市“名校名院名所”工程和杭州“十四五”规划重点建设单位，成立至今，连续获评“杭州市最具影响力新型研发机构”和 “浙江省优秀博士后科研工作站，获批“浙江省智能传感材料与芯片集成技术重点实验室”和“浙江省极弱磁空间及应用技术重点实验室”，获批工信部“5G应用安全创新示范中心（浙江）”，获批国家区块链创新应用试点“区块链杭州市综合试点”，是国家自然基金依托单位，作为主要参与单位，与浙江工业大学等单位一起获批“复杂网络攻击智能检测浙江省工程研究中心”，在杭州科技创新大会上作为首批15家试点单位之一，正式获批“杭州市智能无人系统概念验证中心”，作为承担单位与杭州市数据资源管理局共同发起成立“杭州数据协同创新未来实验中心”，并推动研究院团队牵头或积极参与“空中交通创新实验室”、“数据安全联合创新实验室”和“隐私计算联合创新实验室”等建设。杭州数据安全联盟副理事长单位、浙江省人工智能产业技术联盟理事单位、浙江省网络空间安全协会副理事长单位、杭州市院士专家科创联盟成员单位、杭州市商用密码应用协会会员单位、浙江省5G产业联盟会员单位、国家技术标准创新基地（智能计算）共建单位、杭州区块链技术与应用联合会会员单位、浙江省区块链技术应用协会会员单位。
　　　目前***研究院共有固定研发人员近三百人，其中派驻专家学者共83人，国家级人才37名，专职人才队伍180人，科研人员博士比例近60%，近三年，与行业领军企业共建17个联合实验室，承担横向项目154项，其中百万级项目22项，总合同额8089万元。累计申请发明专利442件，其中PCT22件，授权专利142件。成员所在团队先后主持或参与了国家重点研发计划、省重点研发计划以及企业合作开发项目多项，其中百万级以上项目5项，拥有工信部5G应用安全创新示范中心和5G安全靶场实验室，参与获批“复杂网络攻击智能检测浙江省工程研究中心”。团队实验办公场地1600多平米，实验设备资产1900余万元，***研究院具备良好的实验环境和优秀的科研团队，能够满足本课题所需要的支撑条件和成果交付。
　　　参与单位5：公司成立于2005年11月，注册资金5184万元，是一家专注于网络信息安全领域的国家高新技术企业、专精特新“小巨人”企业。2015年7月在新三板挂牌，证券代码：833029。2013年获评“国家高新技术企业”、2017年获评“科技型中小企业”、“浙江省电子信息50家成长性特色企业”、“浙江省第三批大数据应用示范企业”、“信息安全技术省级高新技术企业研究开发中心”等。是浙江省网络安全行业创新型十强企业。作为国内关键信息基础设施运营单位是浙江省网络安全头部企业。本着“懂业务、更安全”的理念，以用心的服务成就客户价值。公司产品已成功服务于全国20多省份，超过1000家企业，在业界得到了广泛好评。
　　　公司通过产学研项目攻坚突破过程中积累的知识产权和项目经验，参与行业标准制定，提升行业的整体治理水平，推动产业进步。先后加入全国信息标准化委员会、中国通信标准化协会，参与多项国家标准、行业标准制定，已发布实施18项，含国家标准3项。拥有授权发明专利52项，技术成果已在20多个省份超1000家企业转化落地。
　　　公司承担了“工信部2019年制造业高质量发展专项-工业互联网网络安全公共服务平台项目”、“2020年度省重点研发计划项目-工业互联网数据保护与信息安全关键技术研究及应用-面向工业互联网安全的数据保护与应急指挥关键技术及应用研究”、“2019年杭州市5G产业项目-基于SDN/NFV 的5G 网络性能监测公共服务平台”等部、省、市项目。联合****集团浙江公司等单位申报的“基于通信新技术融合互联网+治理不良语音 我要静静解决方案”、“网络信息安全一键式智能应急平台”等4个项目连续三年入选工信部电信和互联网行业网络安全试点示范项目、“网络安全策略可视与分析平台V2.0安全管理平台”获得公安部颁发的“计算机信息系统安全产品销售许可证”，通过攻坚新技术开发新产品实现创新引领。
　　　
5.2项目组成员简介与研究成果
5.2.1 项目组成员简介
　　　项目组团队共126人，包括博士17 名，硕士41人。具备正高级工程师资质3人。项目成员主要研究方向包含计算机科学与技术、密码应用、网络安全、软件供应链安全、信创等。具备长期积累的研究基础、独特的技术方法、丰富的实践经验等。项目成员曾多次主持或参与国家级、省部级重大科研项目。发表多篇国际SCI期刊、SCI、中文核心期刊论文。
1)项目负责人情况
　　　**1983年出生，男，**联合创始人，高级工程师，信创工委会WG34组副组长;国家信息安全标准化TC260 WG5专家成员；被聘请为杭州第19届亚运会供应链专项检查专家并获表彰、杭州数据安全联盟专家委员会专家委员、浙江省网络空间安全协会专家库入库专家、中国信通院聘请为中国信通院软件供应链安全社区专家。负责**公司技术团队管理工作，主要研究领域包括软件供应链安全、安全开发管理、DevSecOps,是数字应用安全领域的探索者，主持研发基于AI智能动态应用安全检测和防护系统、开源软件组成分析系统，产品已市场化应用，其中“开源软件组成分析系统”被评为国家工信部等十二部委“2022网络安全示范试点项目”。拥有多项核心技术的发明专利，主导或参与国家、行业、团体标准项目近10项。
5.2.2 项目团队成员情况
　　　成员一：博士，硕士生导师。研究方向包括软件供应链安全、可信软件、软件工程、机器学习等，近五年来以第一作者或合作作者身份，共计在ICSE，ASE，TSE等软件工程领域重要国际期刊会议上发表论文11余篇。先后参与了国家自然科学基金“可信软件基础研究”重大研究计划重点项目“面向国家电子税务系统的可信软件试验环境与示范应用”，“多维在线跨语言Calling Network建模及其在可信国家电子税务软件中的实证应用”，国家自然科学基金青年项目“基于多层网络理论的软件系统结构分析和行为建模研究”，工信部重点研发项目“2023年产业基础再造和制造业高质量发展专项-工业互联网企业数据勒索攻击智能识别与风险响应系统”，国家金税三期工程等多项科研项目，研究和开发了软件静动态分析及软件代码自动生成等相关可视化应用。相关成果申请或授权国家发明专利多项。
　　　成员二：中共党员，长期从事人工智能安全和硬件安全防护技术前沿技术研究工作，联合供应链管理中心成立智慧家庭生态创新实验室，并完成科创课题19项；完成信息安全共性技术国家工程研究中心杭州分中心、浙江省企业研究院等科技平台申报；参与由中国质量认证中心牵头，中关村智用人工智能研究院组织的智量未来创新联合体，为国家级项目做好相关基础工作；多年来累计牵头发布多项行业标准、参与1项ISO 标准编制，牵头多项国家重要课题和重大安全及算力网络项目研发攻关，为人工智能安全和硬件安全防护技术方面做出重大贡献。
　　　成员三：**大学**学院研究员，博士生导师。2018年获**大学博士学位。长期致力于形式化方法、软件工程、人工智能、软件及系统安全等领域研究工作，并取得多项重要研究成果。先后在S&P、CCS、ICSE、TSE、TDSC、FM等系统安全、形式化方法及软件工程领域国际顶级会议或期刊发表CCF A类论文20余篇（含一作或通讯18篇），两次获得CCF A类会议ICSE的最佳论文奖，并获得了ACM SIGSOFT亮点研究奖（国内唯一）。目前担任CCF形式化方法专委会执行委员，软件工程领域CCF A类国际顶级会议ICSE、ISSTA等程序委员会委员。近五年主持国家重点研发计划子课题、浙江省尖兵攻关项目、国家自然科学基金青年项目等重大项目多项，与加州大学伯克利分校、UIUC、新加坡国立大学**教授（博后导师）、新加坡管理大学**副教授（博士及博后导师）、华为新加坡谢尔德实验室、AISG在人工智能安全领域保持稳定合作，已领导研发了多个人工智能系统测试工具平台，包括**、**、**、**等。
　　　成员四：***，教授，博士生导师，主要从事信息安全、密码学研究，先后主持或参与了国家863计划、973计划、国家重点研发计划、省重点研发计划以及国家自然科学基金、浙江省科技重大项目、省自然科学基金项目的研究工作，发表高水平学术论文50余篇。现为***研究院网络空间安全中心高级研究员、***安全**国家工程实验室顾问委员会委员、“***安全学报”编委、***信息安全重点实验室学术委员会副主任。2016年获浙江省保密工作二等功，2017年获中国电子学会科学技术二等奖。2019年获浙江省科学技术进步二等奖。
　　　成员五:****科技股份有限公司总经理，杭州市D类高层次人才、****大学实务导师、****技术学院行业导师、浙江省职业经理人协会副会长、浙江省互联网协会2018年创新人物。 深耕网络安全领域领域多年，带领团队持续技术创新，获得授权发明专利43项，包括国际专利2项 ，其中第一署名发明专利32项，发表学术论文1篇。牵头研发的《基于安全可信的数字化水文通信系统与示范应用》入选2024年浙江省通信学会科技进步一等奖。积极参与省部级重大科技项目，包括浙江省水利厅、浙江省发展和改革委员会“五大工程”通信双保 障（一、二期）。浙江省科技厅《工业互联网数据保护与信息安全关键技术研究及应用-面向工业互联网安全的数据保护与应急指挥关键技术及应用研究》。浙江省科技厅《支撑行业应用的数据服务系统研究及应用示范》。工信部《2020年工业互联网创新发展工程项目-工业企业网络安全综合防护平 台项目》。工信部《2019年工业互联网创新发展工程—工业互联网网络安全公共服务平台项目》。
　　　成员六：创始人、CEO。深耕信息安全领域15余年，一直致力安全技术研究和发展；拥有多项安全核心技术发明专利；被聘请为杭州数据安全联盟专家委员会专家委员、浙江省网络空间安全协会专家库入库专家、中国信通院聘请为中国信通院软件供应链安全社区专家；主导参与《基于安全检测插件的Web应用系统安全检测技术规范》省级地方标准编制，同时参与金融、数字政府、医疗、教育等众多行业安全体系规划和建设。
　　　成员七：教授，博士生导师，省级领军人才，天地一体信息技术国家重点实验室客座教授，山东未来网络研究院特聘专家。近年来连续主持和参与了科技部网络空间安全重点研发计划、国防科工局基础科研重点项目、工信部工业互联网高质量发展专项以及军委科技委预研、培育类课题等多项。主要从事工业互联网安全与智能防护技术等领域的科研和教学工作，近年来重点关注人工智能欺骗、深度学习智能推演和军工工业数据安全的最新研究动态，在智能安全防护与威胁检测分析领域取得了一系列研究成果。搭建了国内首台面向船舶装备航行安全的网络安全漏洞分析与防护技术验证平台，形成了人工智能安全防护与图谱安全推演工程平台，以及围绕数控机床、工业控制器、工业软件等军工科研生产环境下的网络安全、数据安全防护技术。
　　　成员八：****同志为****技术有限公司首席科学家、※※※部门总经理，长期从事家庭物联网网络安全产品研发工作，在高安全认证、家居安全等领域首创多项技术，并规模应用。授权40 余项专利、获得13 项省部以上科技奖项，于2018 年被授予国务院特殊津贴人才荣誉，2019 年被授予杭州市B 级高层次人才荣誉，2020 年被授予杭州“万人计划”科技创新领军人才荣誉。
　　　成员九：高级工程师、博士，研究方向为网络安全。20多年电信行业从业经验，先后就职两家省级通信运营商公司，主管网络安全。公司创新评估专家组成员、创新工作室负责人，多次获得集团、省公司创新先进个人、岗位创新优秀工作者、电信和互联网行业“网络安全优秀工作者”、中国通信企业协会通信网络安全服务能力评定专家、*电信和互联网行业网络安全专家等称号。带领团队两次入选集团公司创新孵化项目，十余次入选省公司创新项目，多次获得集团、省公司科技创新一、二、三等奖。
　　　成员十:****科技股份有限公司董事、副总经理，杭州市 D 类高层次人才。****大学工程博士生，正高级工程师（信息安全），2014年受聘为杭州市工业和信息化专家。2017年开始成为浙江省科技专家库成员（技术专家）, 2019年受聘为中国信息通信研究院电信网络诈骗治理支撑与服务中心高级反诈专家，杭州市首届数字工匠。作为项目负责人主持杭州市重大科技专项《信息安全应急指挥与智能防御云服务平台研发及示范应用》，实施周期2017.1-2019.12，2020年7月已通过验收。 2019年工信部项目-工业互联网创新发展工程-《工业互联网网络安全公共服务平台》，实施周期2019.1-2020.12。作为核心成员参与浙江省2020年度省重点研发计划项目《面向工业互联网安全的数据保护与应急指挥关键技术及应用研究》 和2020年工信部项目-工业互联网创新发展工程-《工业企业网络安全综合防护平台》。在《电信科学》、《电信技术》、《IEEE Internet of Things Journal 》、《IEEE Access》等行业期刊发表论文10余篇。
　　　成员十一:博士，硕士生导师，中共党员，浙江省计算机学会云计算和信息安全专委会委员。曾就职于行业龙头企业，负责云原生服务网格中性能优化与智能运维相关特性的研究工作。主持中央高校基本科研业务费专项资金资助-新教师创新基金项目、概念验证基金项目、企业科研协作项目等科研项目4项。作为骨干成员深度参与国家重点研发计划、浙江省重点研发计划等科研项目6项。在WWW、IWQoS、IEEE NETWORK等国际顶级会议和期刊发表论文多篇，受理/授权国家发明专利7项，出版专著2本。曾获电子工业出版社2023年度优秀作者，指导学生获得全国大学生信息安全竞赛国家二等奖。
　　　成员十二:博士，硕士生导师，《信息网络安全》青年编委。主要从事可搜索加密、茫然存储技术和密态数据库方面的教学与科研工作。主持“高效可搜索加密方案构造关键技术研究”国家自然科学基金、香港狮子山实验室基金、安徽师范大学开放课题（已结项）3项，参与 “两端模式密态数据库安全计算理论及关键技术研究” 国家自然科学基金项目、“自然社会安全行为理解与智能预警关键技术研究”国家自然科学联合基金重点支持项目等研究工作。在IEEE TSC、TKDE、TDSC等国际期刊及国际会议上发表论文10余篇，受理/授权国家发明专利6项。
　　　成员十三：****，中共党员，****部门副总经理，作为本项目的重要领导者之一，具备多年软件供应链安全治理及信息技术研发的丰富经验，特别是在异构复杂软件供应链的风险评估与治理领域有着深厚的技术积累和管理经验。领导团队深入研究了源代码与二进制代码分析技术，推动了多个关键技术的突破，包括软件成分精准识别、风险量化评估及软件供应链安全治理示范应用等方面。在项目管理方面，副总擅长组织跨企业协同创新，能够高效调动研发团队的积极性，并确保项目按计划推进。同时，积极推动技术成果的转化与落地，致力于提升我国关键基础行业的软件安全水平，尤其在信创国产化背景下，带领团队完成了多个具有行业影响力的项目示范应用，奠定了坚实的基础。推动项目从研发到应用的全链条实现，为我国关键领域的软件供应链安全治理提供了有力保障。
　　　成员十四：中共党员，****部门副总经理，1983年11月出生，长期从事网络能力开放、家庭算力网络设备与平台、家庭物联网网络安全产品研发工作，在家宽网络、家居安全等领域，首创多项技术并规模应用，授权10余项专利、获得2项省部以上科技奖项。
　　　成员十五：****，中共党员，****部门副总经理，作为本项目的重要领导者之一，具备多年软件供应链安全治理及信息技术研发的丰富经验，特别是在异构复杂软件供应链的风险评估与治理领域有着深厚的技术积累和管理经验。领导团队深入研究了源代码与二进制代码分析技术，推动了多个关键技术的突破，包括软件成分精准识别、风险量化评估及软件供应链安全治理示范应用等方面。在项目管理方面，副总擅长组织跨企业协同创新，能够高效调动研发团队的积极性，并确保项目按计划推进。同时，积极推动技术成果的转化与落地，致力于提升我国关键基础行业的软件安全水平，尤其在信创国产化背景下，带领团队完成了多个具有行业影响力的项目示范应用，奠定了坚实的基础。推动项目从研发到应用的全链条实现，为我国关键领域的软件供应链安全治理提供了有力保障。
　　　成员十六：****，中共党员，****部门副总经理，1983年11月出生，长期从事网络能力开放、家庭算力网络设备与平台、家庭物联网网络安全产品研发工作，在家宽网络、家居安全等领域，首创多项技术并规模应用，授权10余项专利、获得2项省部以上科技奖项。
　　　成员十七：项目参与单位2课题骨干，男，2009年毕业于杭州电子科技大学，现任项目团队负责人。主要负责复杂软件供应链中的源代码分析技术研究和风险评估框架的构建工作，在项目中领导开发了源代码与二进制代码的综合分析工具，实现了代码特征快速提取和软件包成分精准识别。在微服务架构和容器化部署环境下，※※※还负责构建了供应链安全监控系统，提升了供应链安全事件的响应速度与处理能力。积极参与供应链安全治理的标准化工作，推动了供应链风险预警机制的建立。
　　　成员十八：项目参与单位2课题骨干，女，2017年毕业于东北石油大学，现任项目参与单位2安全治理团队技术负责人。她主要负责开源软件成分分析技术的研发和供应链安全漏洞的自动化修复。在项目中，※※※参与开发了开源组件的漏洞检测与修复系统，通过深度学习算法和静态分析相结合的方式，显著提高了漏洞检测的精确度。她还参与了企业级开源安全治理平台的建设工作，制定了开源软件使用策略和安全治理规范，帮助企业实现了开源组件的全生命周期安全管理。
　　　成员十九：项目参与单位2课题骨干，男，2018年毕业于浙江工业大学，现任项目参与单位2高级工程专员，专注于二进制代码分析与特征提取技术。在项目中主要负责开发异构复杂软件供应链的二进制代码分析平台，实现了对不同架构软件包的成分识别和风险评估。还领导了软件漏洞的可达性验证技术的研发，推动了多个行业供应链安全治理项目的实施，特别是在金融和电信行业，显著提升了企业的供应链安全管理能力。
　　　
5.2.3 研究理论基础
项目组在代码分析、代码安全、软件供应链安全领域等技术领域具有较好的研究基础。近五年先后发表相关高水平学术论文20余篇。
　　①　****SBOM的软件供应链安全关键技术****（核心期刊）
　　②　****软件供应链安全治理术****研究
　　③　****,“SeqTrans: Automatic Vulnerability Fix via Sequence to Sequence Learning”, Transaction of Software Enginnering
　　④　****,et al. Relation-based Test Case Prioritization for Regression Testing[J]. Journal of Systems and Software, 2020, 163: 110539.
　　⑤　****.“Test Case Prioritization Based on Method Call Sequences.” In 2018 IEEE 42nd Annual Computer Software and Applications Conference (COMPSAC), vol. 1, pp. 251-256. IEEE, 2018.
　　⑥　****,et al. REMS: Recommending Extract Method Refactoring Opportunities via Multi-view Representation of Code Property Graph. In 2023 IEEE/ACM 31st International Conference on Program Comprehension (ICPC). IEEE 2023
　　⑦　****, et al. Behavior-aware account de-anonymization on ethereum interaction graph[J]. IEEE Transactions on Information Forensics and Security, 2022, 17: 3433-3448.
　　⑧　****. Leveraging Developer Information for Efficient Effort-aware Bug Prediction[J]. Information and Software Technology, 2021, 137: 106605
　　⑨　****, et al. Using K-core Decomposition on Class Dependency Networks to Improve Bug Prediction Model's Practical Performance[J]. IEEE Transactions on Software Engineering, 2019. 
　　⑩　****. “Dynamic structure measurement for distributed software.” Software Quality Journal (SQJ) 26, no. 3 (2018): 1119-1145. 
　　⑪　****. “Dynamic cohesion measurement for distributed system.” In Proceedings of the 1st International Workshop on Specification, Comprehension, Testing, and Debugging of Concurrent Programs, pp. 20-26. ACM, 2016. 
　　⑫　****: “Android Malware Detector Exploiting Convolutional Neural Network and Adaptive Classifier Selection”. COMPSAC (1) 2018: 833-834 (CCF C)
　　⑬　****: “node2defect: using network embedding to improve software defect prediction”. ASE 2018: 844-849 
　　⑭　****: Mutation-based Dependency Generation for Precise Taint Analysis on Android Native Code. IEEE Transactions on Dependable and Secure Computing, 2023, 20(2): 1461-1475.
　　⑮　****. CryptoEval: Evaluating the risk of cryptographic misuses in Android apps with data‐flow analysis. IET Information Security, 2023, 17(4): 582-597. 
　　⑯　****. DeepCatra: Learning Flow- and Graph-based Behaviors for Android Malware Detection. IET Information Security, 2023, 17(1): 118-130. 
　　⑰　****. A Novel Dynamic Android Malware Detection System With Ensemble Learning. IEEE Access, 2018, 6: 30996-31011.
　　⑱　**** GNNDroid: Graph-Learning based Malware Detection for Android Apps with Native Code[J]. IEEE Transactions on Dependable and Secure Computing, 2024. 
　　⑲　****. GlareShell: Graph learning-based PHP webshell detection for web server of industrial internet[J]. Computer Networks, 2024, 245: 110406. 
　　⑳　****. DawnGNN: Documentation augmented windows malware detection using graph neural network[J]. Computers & Security, 2024: 103788. 
　　21　****. BinGo: Identifying Security Patches in Binary Code with Graph Representation Learning[J]. ACM ASIA Conference on Computer and Communications Security (AsiaCCS), 2024. 
　　22　****. BejaGNN: behavior-based Java malware detection via graph neural network[J]. The Journal of Supercomputing, 2023, 79(14): 15390-15414. 
　　23　****. Binprov: Binary code provenance identification without disassembly[C]//Proceedings of the 25th International Symposium on Research in Attacks, Intrusions and Defenses. 2022: 350-363.
　　24　****, Attacks and Countermeasures on the Network Context of Distributed Honeypots[C]//International Conference on Detection of Intrusions and Malware, and Vulnerability Assessment. Cham: Springer International Publishing, 2022: 197-217.
　　25　****. Enhancing malware analysis sandboxes with emulated user behavior[J]. Computers & Security, 2022, 115: 102613. 
　　26　****: A large-scale security patch dataset[C]//2021 51st Annual IEEE/IFIP International Conference on Dependable Systems and Networks (DSN). IEEE, 2021: 149-160. 
　　27　****: Combating sandbox evasion via user behavior emulators[C]//Information and Communications Security: 21st International Conference, ICICS 2019, Beijing, China, December 15–17, 2019, Revised Selected Papers 21. Springer International Publishing, 2020: 34-50. 
　　28　**** Investigating the Impact of Multiple Dependency Structures on Software Defects[C]//2019 IEEE/ACM 41st International Conference on Software Engineering (ICSE). IEEE, 2019: 584-595. (ICSE, CCF A)
　　29　****. Towards Characterizing Bug Fixes through Dependency-level Changes in Apache Java Open Source Projects[J]. Science China Information Sciences, 2022, 65(7): 172101. (SCIS, CCF A)
　　30　****. RMove: Recommending Move Method Refactoring Opportunities using Structural and Semantic Representations of Code[C]//2022 IEEE International Conference on Software Maintenance and Evolution (ICSME). IEEE, 2022: 281-292. (ICSME, CCF B)
　　31　****. Towards Demystifying the Impact of Dependency Structures on Bug Locations in Deep Learning Libraries[C]//Proceedings of the 16th ACM/IEEE International Symposium on Empirical Software Engineering and Measurement. 2022: 249-260. (ESEM, CCF B)
　　32　****. REMS: Recommending Extract Method Refactoring Opportunities via Multi-view Representation of Code Property Graph[C]//Proceedings of the 31st International Conference on Program Comprehension. 2023. (ICPC, CCF B)
　　33　****. An Empirical Study of Architectural Changes in Code Commits[C]//Proceedings of the 12th Asia-Pacific Symposium on Internetware. 2020: 11-20. (INTERNETWARE, CCF C)
　　34　****. Using k-core decomposition on class dependency networks to improve bug prediction model's practical performance[J]. IEEE Transactions on Software Engineering, 2019, 47(2): 348-366. (TSE, CCF A)
　　35　****. Active hotspot: an issue-oriented model to monitor software evolution and degradation[C]//2019 34th IEEE/ACM International Conference on Automated Software Engineering (ASE). IEEE, 2019: 986-997. (ASE, CCF A)
　　36　****. FVT: a fragmented video tutor for" dubbing" software development tutorials[C]//2019 IEEE/ACM 41st International Conference on Software Engineering: Software Engineering Education and Training (ICSE-SEET). IEEE, 2019: 95-99. (ICSE, CCF A)
　　37　****: a tool framework for extensible eNtity relation extraction[C]//2019 IEEE/ACM 41st International Conference on Software Engineering: Companion Proceedings (ICSE-Companion). IEEE, 2019: 67-70. (ICSE, CCF A)
　　38　HODOR: Shrinking Attack Surface on Node.js via System Call Limitation（CCF A）
　　39　Towards Optimal Concolic Testing（CCF A）
　　40　K-ST: A Formal Executable Semantics of the Structured Text Language for PLCs（CCF A）
　　41　FairRec: Fairness Testing for Deep Recommender Systems（CCF A）
　　42　Towards Concolic Testing for Hybrid Systems（CCF A）
　　43　Isolation-based Debugging for Neural Networks（CCF A）
　　44　VERIFI: Towards Verifiable Federated Unlearning（CCF A）
　　45　Should We Learn Probabilistic Models for Model Checking? A New approach and an Empirical Study（CCF B）
　　46　Better Pay Attention Whilst Fuzzing（CCF A）
　　47　VeRe: Verification Guided Synthesis for Repairing Deep Neural Networks（CCF A）
　　48　Attack as Detection: Using Adversarial Attack Methods to Detect Abnormal Examples（CCF A）
　　49　TestSGD: Interpretable Testing of Neural Networks Against Subtle Group Discrimination（CCF A）
　　50　图像对抗样本检测综述（CCF A）
　　51　Copy, Right? A Testing Framework for Copyright Protection of Deep Learning Models（CCF A）
　　52　QuoTe: Quality-oriented Testing for Deep Learning Systems（CCF A）
　　53　NeuronFair: Interpretable White-Box Fairness Testing through Biased Neuron Identification（CCF A）
　　54　Which neural network makes more explainable decisions? An approach towards measuring explainability（CCF B）
　　55　RobOT: Robustness-Oriented Testing for Deep Learning Systems（CCF A）
　　56　Automatic Fairness Testing of Neural Classifiers through Adversarial Sampling（CCF A）
　　57　Attack as Defense: Characterizing Adversarial Examples using Robustness（CCF A）
　　58　Improving Neural Network Verification through Spurious Region Guided Refinement（CCF B）
　　59　HRPDF: A Software-Based Heterogeneous Redundant Proactive Defense Framework for Programmable Logic Controller（CCF B）
　　60　White-box Fairness Testing through Adversarial Sampling（CCF A）
　　61　Towards Interpreting Recurrent Neural Network through Probabilistic Abstraction（CCF A）
　　62　Adversarial Sample Detection for Deep Neural Networks through Model Mutation Testing（CCF A）
　　63　Automatically ‘Verifying’ Discrete-Time Complex Systems through Learning, Abstraction and Refinement（CCF A）
　　64　Towards ‘Verifying’ a Water Treatment System（CCF A）
　　65　TeDA: A Testing Framework for Data Usage Auditing in Deep Learning Model Development（CCF A）
　　66　Importance Sampling of Interval Markov Chains（CCF B）
　　67　FAST: Boosting Uncertainty-based Test Prioritization Methods for Neural Networks via Feature Selection（CCF A）
　　68　Interpretability based Neural Network Repair（CCF A）
　　69　****. Active hotspot: an issue-oriented model to monitor software evolution and degradation[C]//2019 34th IEEE/ACM International Conference on Automated Software Engineering (ASE). IEEE, 2019: 986-997. (ASE, CCF A)
　　70　****. FVT: a fragmented video tutor for" dubbing" software development tutorials[C]//2019 IEEE/ACM 41st International Conference on Software Engineering: Software Engineering Education and Training (ICSE-SEET). IEEE, 2019: 95-99. (ICSE, CCF A)
　　71　****: a tool framework for extensible eNtity relation extraction[C]//2019 IEEE/ACM 41st International Conference on Software Engineering: Companion Proceedings (ICSE-Companion). IEEE, 2019: 67-70. (ICSE, CCF A)
　　72　VeRe: Verification Guided Synthesis for Repairing Deep Neural Networks（CCF A）
　　73　TeDA: A Testing Framework for Data Usage Auditing in Deep Learning Model Development（CCF A）
　　74　Isolation-based Debugging for Neural Networks（CCF A）
　　75　Interpretability based Neural Network Repair（CCF A）
　　76　国际SCI期刊:Optimizing ***  in RF-Charging Multi-Hop IoT Networks
　　77　**** erasure for time***augmentation；DAFuzz: **** of in-memory data stores（SCI）
　　78　Identification of **** Based on Raw Traffic；A CAN Bus Security *** for Automotive *** Systems；GSA-Fuzz: ***Mutation with Gravitational ****. Security and Communication Networks；（4区）
　　79　Multiple **** Recognition Based on a Deep Learning Framework and Cross-loss Training；***** Based on Pre-Trained Hidden Markov Models（3区）
　　80　Deep Learning for *** （2区）
　　81　多步***关键技术研究展望；基于**S应用场景下的***密码应用方案。
5.2.4 项目团队专利情况
　　①　开源代码※※※方法、装置及计算机可读存储介质，CN 1****90256 B 
　　②　代码克隆检测方法、系统及****可读存储介质ZL 20****77591
　　③　基于代****数自动重构方法及装置、电子设备 CN 1****141 B
　　④　**漏洞检测的方法、装置、终端及存储介质，CN 11****90256 B 
　　⑤　一种基于***的安全检测方法	ZL202****331173.8
　　⑥　一种基于语****的软件缺陷溯源方法202010****86.9
　　⑦　基于软****联分析的软件缺陷定位方法2020****150127.3
　　⑧　一种切片级特征******漏洞精准检测方法 202****48200.3 
　　⑨　基于GO语言的****跟踪方法、装置和电子装置20****609333
　　⑩　账号****漏洞检测方法、系统以及存储介质2022 1****7.8
　　⑪　一种针****安全检测的处理方法及装置2023 1****7455.9
　　⑫　一种越权****权逻辑漏洞检测方法202****376357
　　⑬　一种漏洞****法、系统、服务器及存储介质20****1407766
　　⑭　一种账号枚****方法、系统及存储介质2****1376339
　　⑮　一种应用****洞检测方法2023 1****7455.1
　　⑯　一种基于修改****技术的漏洞验证方法2023 1****7635
　　⑰　一种基于大****生产-测试代码协****演化方法 20****84718.9 
　　⑱　基于图嵌入的****代码差异获取方法 2023****68765.1
　　⑲　一种基于****译的测试用例版本自动迁移方法 20****584977.8
　　⑳　一种※※※组件化的※※※方法及装置，授权号：CN 1****24333 B 
　　21　***方法、网关、传感器和电子设备	PCT/CN****22/071463
　　22　***方法、传感器和汇聚设备	2022****9164.8
　　23　基于***方法和设备	2022****56351.5
　　24　RFID系统的**方法和**系统	2022****561599.7
　　25　基于***的身份认证与访问控制系统、方法和设备	20****238849.7
　　26　一种搭建***系统的方法、装置和系统	202****289679.1
　　27　一种***的文件加密管理系统及控制方法	2022****2649.0
　　28　一种***的**检测模型预训练方法	20****1492686.1
　　29　一种***的设备离线检测方法	ZL20****0364543.6
　　30　可动态扩展计算资源***的方法及装置	ZL202****04579.2
　　31　一种基于***的恶意软件行为检测与分类系统	ZL2****11254605X
　　32　一种基于***的僵尸网络检测方法	ZL2****254593.0
　　33　一种**安全防护方法、系统、架构及介质专利	ZL 202****15650.7
　　34　一种基于**访问控制系统	20231****268.8
　　35　一种加密硬盘***管理方法与系统	2023****0691.6
　　36　一种基于***的安全通信系统	20231****729.X
　　37　物联网设备的****的协同优化方法	2023****53456.6
　　38　基于层次****安全应急处置方法及系统	ZL 2017****4.5
　　39　一种安全应急处****价体系构建方法及系统	ZL 2****0363175.2
　　40　一种基于深度神经网****的网元容量分析与预测的方法ZL 201****59853.0
　　41　一种利用键值****高黑名单准确率的方法及系统ZL202****431.6
　　42　一种木马图片****、系统及计算机可读存储介质	ZL 202****886.6
　　43　一种GoIP诈骗窝点****方法、系统及计算机可读存储介质ZL2****0045845.7
　　44　一种基于用户****析的高危操作识别方法及系统ZL20****95033.0
　　45　基于算法组件****未知诈骗的识别方法及系统ZL20****0440913.X
　　46　一种骚扰诈****防范方法及系统ZL202****613857.5
　　47　一种基于身份信****骚扰诈骗电话的防范方法及系统ZL2****060892.5
　　48　基于FRR软件****集群的IP封堵方法、系统及介质ZL20****161357.9
　　49　Deepfake视频****方法、系统及可读存储介质ZL2****11671206.8
　　50　基于实时流****数据故障检测方法、系统及介质ZL 202****88942
　　51　一种自适应增强****算法安全的算法组件库ZL202****473597.0
　　52　物联网通信方法、系****算机可读存储介质ZL2****457786.9
　　53　物联网数****全通信方法、系统及计算机可读存储介质ZL2****621002.1
　　54　LVM故障****速恢复方法、系统和计算机可读存储介质ZL 2023****77449.2
　　55　水文数据的****法、系统及可读存储介质ZL 202****82632.1
　　56　金融反欺诈风险****法、系统及可读存储介质ZL 202****635.5
5.2.5 项目团队相关软件著作权情况
　　①　软件代码****系统 V1.0 202****5267
　　②　任务****指令系统 V1.0 20****0109790 
　　③　***开源软件安全分析系统v2.0 2022****772108
　　④　***Web应用安全检测系统v2.0 20****0772108
　　⑤　***网站恶意代码防治系统v1.0 2022****4388
　　⑥　***交互式应用安全检测与免疫防御系统v1.0 2022S****30488
　　⑦　***安全开发一体化管理平台 2022****30489
　　⑧　***面向关键信息基础设施的安全能力底座软件V1.0	20****1653780
　　⑨　***网站安全检测平台软件V1.0	202****117916
5.2.6 承担单位现有软件成分分析产品情况
　　　承担单位作为国内软件供应链安全领域的领航者，一直专注于软件供应链安全领域前沿技术和产品的研发。先后自主完成了基于包管理器的成分分析、基于代码插桩的成分及安全检测等一系列核心技术，自2019年起先后研发了交互式应用安全检测、软件成分分析、软件供应链安全治理等平台，产品与解决方案已广泛应用于金融、运营商、政务、医疗、能源、军工等关键信息行业，相关技术和产品已在各行业场景中得到了验证。
　　　在软件供应链安全技术研究与行业应用过程中，公司研发并积累了软件供应链相关的组件库、漏洞库、许可库、代码片段库等核心知识库数亿量级。兼容CNNVD、CNVD、NVD等权威漏洞库，能够覆盖C、C++、Java等数十种常见主流编程语言。现有软件成分分析产品的资质与荣誉如下图所示。

图 34 软件分析产品资质与荣誉
