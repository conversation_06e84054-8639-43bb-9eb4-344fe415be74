
项目阶段性总结报告：

　　　重点描述项目阶段研究的主要内容和过程、重要结论和成果、关键技术研发等内容，一般包括如下内容：
　　　（一）项目阶段研究的要点和调整情况。简要描述阶段研究任务的完成情况。如研究内容根据研究发展状况和项目进展情况作了调整，简述变动原因。
　　　（二）研究工作主要进展和阶段性成果。参照任务书中的主要研究内容/任务，针对各个技术点，自拟标题，按照研究流程或技术点，分章节论述。应完整描述研究的阶段性成果，包括基本理论、研究假设、研究方法、试验/实验方法、研究过程等，提供必要的图、表、实验及观察数据等信息，并对使用到的关键装置、仪表仪器、材料原料等进行描述和说明。
　　　（三）下阶段研究计划。阐述下阶段研究计划，包括研究目标、主要任务、研究重点、进度安排等。


当前完成情况：
(1)核心技术指标完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值	是否里程碑成果
技术发明专利（授权）	1项	1项	6项	是
覆盖编程语言种类	4种（C、C++、Java、JavaScript）
	2种	4种（C、C++、Java、JavaScript）	否
技术发明专利（申请）	9项	5项	5项	是
(2)分析准确率指标完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值
源代码级成分分析准确率	60%	40%	85%
二进制代码级成分分析准确率	60%	50%	75%
软件自研率分析准确度	70%	60%	90%
漏洞检查风险分析准确度	80%	60%	90%
许可证合规风险分析准确度	80%	60%	90%
(3)扩展技术能力指标完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值
支持编程语言包管理器分析	3种（Java、Go、JavaScript）	2种	10种（Go, C#, Ruby, Python, 鸿蒙仓颉等）
支持二进制文件格式识别	27种（.exe、.dll、
.so、.bin
.msi、.cab、.hex、
.bin、.rom、.rar、
.zip、.tar、.tbz2、
.tgz、.tbr、.tlz、
.tsz、.txz、.tzst、
.br、.gz、.bz、lz4
.sz、.xz、.zst、.7z
）

	20种	50种（.exe、.msi、.bin、RAR等）
支持处理器类型	5种（🦞Intel、AMD、海光、鲲鹏、龙芯）	3种	10种（龙芯、兆芯、海光、鹲鹏、飞腾等）
(4)数据库规模指标完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值
二进制函数特征向量	1千2百万条	1千万条	2千万条
二进制符号特征向量	2亿条	1亿条	5亿条
二进制函数调用关系图	224万条	100万条	600万条
(5)智能化分析能力指标完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值
漏洞靶向修复补丁生成规则	1582个



	100条	1000条
Java应用软件热修复中间件支持	4种（龙芯JDK、毕昇JDK、阿里巴巴Dragonwell JDK、腾讯Kona JDK）	1种	3种国产信创中间件
源代码漏洞可达性分析CVE支持	1582个	100个CVE漏洞	500个CVE漏洞
软件供应链投毒检测情报数据	3万条	1千条	1万条
SBOM标准格式支持	3种（CycloneDX、SWID、SPDX）	1种	3种
(6)信创兼容性指标完成情况
指标名称	已有指标值	项目完成时指标值
国产信创操作系统兼容性认证	0种	3种
国产信创数据库兼容性认证	0种	1种
国产信创中间件兼容性认证	0种	2种


2.2.2.项目经济指标
本项目经济指标主要包括知识产权成果、学术成果、应用示范和产业化收入等方面。截至2025年中期检查，各项经济指标完成情况如下：
(1)知识产权成果完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值	是否里程碑成果
技术发明专利（申请）	9	5项	5项	是
软件著作权	1	5项	11项	否
(2)学术成果完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值
期刊论文	2	0	2项

(3)应用示范完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值
行业应用示范	0	1个应用示范	6个应用示范
(4)产业化经济效益完成情况
指标名称	已有指标值	中期检查指标值	项目完成时指标值
相关销售收入	0	10万元	800万元
