二、项目主要研发内容及创新点/关键技术
一.主要研发内容
针对国产 SCA 分析能力、风险评估与管控能力不足等问题，将在现有技术积累基础上， 进行面向源代码与二进制代码的软件成分分析技术和异构复杂软件供应链风险评估  体系研究。项目总体研究内容划分为四个部分，  课题 1 面向源代码与二进制代码的软  件成分分析技术研， 课题 2 异构复杂软件供应链的风险评估体系研究，课题 3：构建  异构复杂软件供应链分析及风险评估平台，课题 4：异构复杂软件供应链安全治理的  示范应用。其中课题 1 和课题 2 作为核心技术研究部分，课题 3 基于课题 1、 2 的成  果进行平台研发，课题 4 基于前面的成果进行应用示范。
　1.面向源代码与二进制代码的软件成分分析技术和异构复杂软件供应链风险评估体 系研究，重点在代码特征快速提取技术研究，包括基于增量代码片段基因标记技术的 源代码特征快速提取技术研究、基于自监督学习的跨语言代码克隆检测方法研究、基 于大语言模型开源组件漏洞缺陷提取技术研究；   本部分针对信创国产化环境下软件 供应链复杂异构性和海量数据处理需求，拟开展异构复杂软件代码特征快速提取技术 研究，提升软件成分分析效率、准确性和自动化水平。
2.实现二进制代码特征快速提取技术研究、多源异构供应链软件威胁建模技术研究、 多维度风险指标体系构建方法研究、基于源代码漏洞可达性分析技术研究、二进制软 件包多维度分析评估技术研究和靶向热修复技术等关键技术上寻求突破，最终形成基 于信创国产化场景的异构复杂软件供应链风险管控与治理平台、软件成分分析工具；

3.在代码和许可证特征快速提取、软件包成分精准识别、软件潜在风险多维度量化评 估、漏洞真实可达性检测、组件风险监控预警和靶向热修复等方面建立多语言覆盖的 高可靠、高性能、强通用性的软件供应链安全风险管控与治理能力；   针对软件风险 量化评估难题， 围绕异构复杂软件供应链风险评估所需的通用性、高效性、安全性和 可靠性等要求开展多源异构软件供应链威胁建模研究、多维度供应链风险指标体系构 建及异构供应链风险评估方法研究。为软件供应链的风险评估与治理提供基础。
4.构建完成国防军工、金融、运营商、能源、政务、制造业等行业应用示范。
　在信创国产化的背景下，异构复杂软件供应链的安全问题日益突出，尤其是涉及到国 家关键基础行业（如军工、金融、运营商、能源、政务、制造业等）的软件供应链， 任何风险都可能导致严重的安全事件。针对这些行业的异构复杂软件供应链安全治理 的示范应用场景研究，不仅有助于提高各行业的软件供应链安全防护水平，还能为其 他行业提供参考和借鉴。本课题的通过示范应用验证异构复杂软件供应链安全治理方 案的有效性和适用性，并通过应用中总结经验，形成可推广的安全治理策略和方法。
二.关键技术
　1.面向异构复杂软件供应链场景的高性能、跨平台、跨架构的软件成分分析能力增强 技术研究







1)基于自监督学习和大语言模型的代码特征快速提取技术
2)基于自监督学习的跨语言代码克隆检测方法，实现针对最终为软件
3)基于关键阻塞链识别的漏洞修复传播路径分析方法
4)基于语义匹配与深度学习的二进制函数相似度检测方法
2.面向异构复杂软件供应链的多源威胁建模与风险评估技术研究
1)多源异构软件供应链威胁建模技术
2)多维度风险指标体系构建与分析评估方法
3)基于源代码的漏洞可达性分析技术
3.面向信创国产化场景的异构复杂软件供应链分析及风险评估平台技术 海量开源项目代码特征基因提取及运行时漏洞靶向热修复技术.
基于课题 1 和课题 2 的研究成果通过工程化过程，最终实现研究成果产品转化，主要 包括构建海量开源项目监控与源数据采集系统，拟实现对开源项目等多样化数据源的 自动化监控与增量更新采集。拟构建分布式海量代码特征基因提取系统，  构建一个面 向多数据源的代码特征基因库，针对海量代码处理，解决代码特征基因提取，实现高 效的分布式存储与处理，应对大规模代码分析和特征提取场景。拟构建基于大语言模 型识别开源组件漏洞缺陷提取系统， 自动生成漏洞可达性分析规则和漏洞靶向热修复 补丁，解决人工编写漏洞规则成本高，效率低的问题。依托现有平台技术，  增强现有 软件成分分析平台能力以及软件风险评估与管控能力。
三.主要创新点
　1.拟基于增量代码片段基因标记技术和自监督学习驱动的架构无关指令嵌入方法，实 现源代码和二进制代码的快速特征提取和分析，突破国产 SCA 产品在大规模代码库中 代码特征提取的效率低，检测准确率低的问题。
　2.拟提出包括启发式解包、二进制函数向量相似度检测、二进制到源代码的函数相似 性检测、二进制第三方库模块检测以及二进制函数多维度特征分析等技术，  填补国产 SCA 产品在函数级多维二进制分析领域的空白，突破二进制分析检出率低、误报率高 的问题。
3.拟基于大语言模型开源组件漏洞缺陷提取技术， 自动生成漏洞可达性分析规则和漏 洞靶向热修复补丁，并通过关键阻塞链分析优化漏洞修复的传播路径。有望解决人工 编写漏洞规则成本高，效率低的问题。
4.拟构建多源异构软件供应链的多维度风险评估体系，涵盖技术、管理、知识产权、 可维护性和闭源组件等多个维度的评估指标，结合代码特征与二进制代码成分分析结 果，设计二进制代码维度评估指标，拟实现对异构复杂供应链的多维度、全面性的风 险量化评估，显著提升异构复杂软件供应链中的安全治理能力。





三、项目研发目标及主要技术经济指标


（一）“尖兵计划”榜单攻关任务目标

攻关目标	关键指标全覆盖
对标单位	单位名称 1	Snyk Limite	具体
型号	Snyk Open Source



攻关技术水平 及应用	□达到国际领先水平（国际领跑）
　□达到国际先进水平（国际并跑） ☑达到国内领先水平（国内领跑）
	☑开发出国产化替代技术的技术就绪度达到9 级并实现应用
	应用单位名称	统一社会信用代码







（二）项目研发目标及主要技术经济指标

1.项目预期目标

一.需求关键问题
针对信创国产化场景下的异构复杂软件供应链风险评估与安全治理 ,重点研究面向源  代码与二进制代码的软件成分分析技术 ,构建针对异构复杂软件供应链的风险评估体  系，开展异构复杂软件供应链安全治理的示范应用;突破代码特征快速提取、软件包  成分精准识别、潜在风险量化评估等关键技术，  构建异构复杂软件供应链分析及风险 评估平台。
二.技术成果
　1.填补国产 SCA 函数级多维二进制分析领域空白，突破二进制分析低检出率、高误  报率问题；解决信创环境中异构编处理器、操作系统、异构编译器、编译优化选项等 复杂环境二进制成分分析的难题。
2.突破大规模代码库中特征提取效率低的瓶颈 ,研发大语言模型漏洞缺陷提取系统， 自动生成漏洞可达性分析规则和靶向热修复补丁 ,解决人工编写成本高、效率低的问 题。
3.首创软件多源异构复杂供应链的风险评估体系 ,实现风险量化评估并提供漏洞可达 性分析、漏洞靶向热修复等能力为供应链安全治理管控提供支撑。
　三.理论成果 : 申请技术专利 11 项（授权不少于 6 项，受理不少于 5 项），软著 11 项，发表高水平论文 2 项。
四.应用成果:在国防军工、金融、运营商、能源、政务等行业应用示范。
五.2026 年 12 月 31  日前完成项目研发。





2.成果考核指标、考核方式/方法
　列出与项目目标紧密相关的主要、核心标志性成果，成果产生时间需为 2025 年起。要求指标清晰具体可考核，项目成果需包含数量指标、技术指标、质量指标、应用指标和产   业化指标。
预期成果与考核指标表
预期成果	考核指标	
考核方式
（方法）
及评价手
段

成果名称	
成果类型	指标类型（数
量指标、技术
指标、质量指
　标、应用指标   和产业化指标）	
指标名称 与内容	
已有指标值/
状态	
项目中期检
查时指标值	
项目完成时 指标值	
是否里程碑
成果	
里程碑成果 形成时间	

重大标志性成果	研发完成 SCA  函数级多维二进制分析系统，突破异构编处理器、操作系统、异构编译器、编译优化
选项等复杂环境二进制成分分析的难题。	
	研发基于大语言模型漏洞缺陷提取系统，实现利用模型生成漏洞可达性分析规则和靶向热修复补丁,
突破人工编写成本高、效率低的问题。	
主要成果 （ 约束性指标	

1	

技术发明专利	

发明专利（申请）	

数量指标	
技术发明 专利	

0	

5	

5	

是	

2025-12-31	
发明专利 受理
	

2	

技术发明专利	

发明专利（授权）	

技术指标	
技术发明 专利	

0	

1	

6	

是	

2026-12-31	
技术发明
专利授权 证书






	

3	

软件著作权	

其他	

数量指标	
软件著作
权	

0	

5	

11	

否		
软件著作
权登记证
书
	

4	

期刊论文	

新理论	

技术指标	

期刊论文	

0	

0	

2	

否		

发表论文
	

5	

产品研发技术指标	

其他	

技术指标	覆盖不少
于四种编
程语言(C、
C++、Java、
JavaScript)	

1种	

2 种	

4 种	

否		
第三方测 试报告
	

6	

产品研发技术指标	

其他	

技术指标	
源代码级
成分分析
的准确率	

30%	

40%	

85%	

否		
第三方测 试报告
	

7	

产品研发技术指标	

其他	

技术指标	二进制代
码级成分
分析的准 确率	

40%	

50%	

75%	

否		
第三方测 试报告






	

8	

产品研发技术指标	

其他	

技术指标	
软件自研
率分析准 确度	

50%	

60%	

90%	

否		
第三方测 试报告
	

9	

产品研发技术指标	

其他	

技术指标	
漏洞检查
风险分析 准确度	

50%	

60%	

90%	

否		
第三方测 试报告
	

10	

产品研发技术指标	

其他	

技术指标	
许可证合
规风险分
析准确度	

50%	

60%	

90%	

否		
第三方测 试报告
其他成果 （ 预期性指标	

1	

产业化指标	

其他	

产业化指标	使用本项
目成果的
相关销售
收入	

0	

10 万	

800 万	

否		
第三方审 计报告
	


2	


产业化指标	


其他	


产业化指标	　完成国防    军工、金融、 运营商、能
源、政务、 制造业等
行业应用 示范。	


0	

1 个应用示
范	

6 个应用示
范	


否		

用户使用 报告






	






3	






产品研发额外技术指标	






其他	






技术指标	支持基于
开发语言
包管理器
　的分析技  术，漏洞检
查、许可证 合规等风  险分析，覆
盖不少 10
种编程语
言(如：Go, C#,Ruby,P ython,鸿蒙 仓颉等)	






1种	






2 种	






10 种	






否		





第三方测 试报告
	



4	



产品研发额外技术指标	



其他	



技术指标	支持识别
　分析.exe、. msi、.bin、 RAR 等二  进制文件
格式或压
缩格式类
型不少于
50 种	



10 种	



20 种	



50 种	



否		


第三方测 试报告
	




5	



产品研发额外技术指标	



其他	



技术指标	支持基于
　信创处理    厂商龙芯、 兆芯、海光、
鲲鹏、飞腾， 非信创厂   商 MIPS、S
PARC、PO
WER、Intel、 AMD 等不	



/	



3 种	



10 种	



否		



第三方测 试报告






					少于 10 种 类型处理
器编译的
二进制文 件分析						
	









6	









产品研发额外技术指标	









其他	









技术指标	建立面向
信创异构
　复杂环境   (跨处理器、 跨编译器、 跨 OS 平    台)软件基
因数据库， 二进制函
数特征向
　量不少于  2 千万条， 二进制符
号特征向
　量不少 5   亿条、二进 制函数调
用关系图
不少于 60
0 万条	









/	




二进制函数
特征向量不
少于 1 千万
条，二进制
符号特征向
量不少 1 亿
条、二进制
函数调用关
系图不少于
100 万条	




进制函数特
　征向量不少  于 2 千万条， 二进制符号
特征向量不
少 5 亿条、
二进制函数
调用关系图 不少于 600
万条	









否		








第三方测 试报告
	


7	


产品研发额外技术指标	


其他	


技术指标	建立基于
大语言模
型开源组
件漏洞的
靶向修复
补丁生成
与缺陷函	


/	生成不少于 100 条的漏 洞靶向修复
或漏洞可达
性分析规则	生成不少于  1000 条的漏 洞靶向修复
或漏洞可达
性分析规则	


否		

第三方测 试报告






					　数特征提  取系统，生
成不少于
1000 条的 漏洞靶向
修复或漏
洞可达性
分析规则						
	



8	



产品研发额外技术指标	



其他	



技术指标	　实现支持j ava 应用软 件开源组
件漏洞靶
向热修复
技术，兼容 不少于 3
种国产信
创中间件	



/	



1种	



3 种	



否		


第三方测 试报告
	


9	


产品研发额外技术指标	


其他	


技术指标	实现支持
基于源代
码漏洞可
　达性分析  功能，支持
不少于 50
0 个 CVE
漏洞	


/	

支持不少于 100 个 CVE
漏洞	

支持不少于 500 个 CVE
漏洞	


否		


第三方测 试报告
	

10	

产品研发额外技术指标	

其他	

技术指标	支持软件
　供应链投 毒检测,投 毒情报数
据不少于 1 万条	

/	
投毒情报数
据不少于 1
千条	
投毒情报数
据不少于 1
万条	

否		

第三方测 试报告






	


11	


产品研发额外技术指标	


其他	


技术指标	支持分析
和导出不
少于 3 种
SBOM
（软件物
料清单）
标准格式	


/	


1种	


3 种	


否		

第三方测 试报告
	






12	






产品研发额外技术指标	






其他	






技术指标	平台系统
　信创国产  化兼容，完
成不少于
3 种国产
信创操作
　系统兼容 性认证、1
种国产信
创数据库、 2 种国产
信创中间
件间兼容
性认证。	






/	






/	


3 种国产信
　创操作系统   兼容性认证、 1 种国产信   创数据库、2   种国产信创
中间件间兼
容性认证	






否		





第三方测 试报告
其他（约束性指标）		






备注：
1.“考核指标”，指相应成果的数量指标、技术指标、质量指标、应用指标和产业化指标等，其中，数量  指标可以为专利、产品等的数量，论文代表作应注重质量，不以数量作为评价标准；技术指标可以为  关键技术、产品的性能参数等；质量指标可以为产品的耐震动、高低温、无故障运行时间等；应用指  标可以为成果应用的对象、范围和效果等；产业化指标可以为成果产业化的数量、经济效益等。同时， 对各项考核指标需填写申报时已有的指标值/状态以及项目完成时要到达的指标值和具体内容。同时， 考核指标也应包括支撑和服务其他重大科研、经济、社会发展、生态环境、科学普及需求等方面的直  接和间接效益，如对国家重大工程、社会民生发展等提供了关键技术支撑，成果转让并带动了环境改  善、实现了销售收入等。若某项成果属于开创性的成果，  申报时已有指标值/状态可填写“无”,若某项成  果在申报时已有指标值/状态难以界定，则可填写“/”。成果产生时间需为项目实施年起。
2.   “重大标志性成果”，应为重大科学发现、技术突破、产品研发等，可突破卡脖子技术或实现进口替 代，达到国际先进、国内领先水平。单个成果字数不超过 100 字，至少填写 1 项，最多 3 项。
3.“主要成果（约束性指标）”,至少填写 1 项。对口帮扶项目需至少填写 1 项拟在对口支援地区转化的 技术成果名称和内容。
4.“指标名称与内容”，应明确成果相关内容以及类别，如制订的标准涉及哪个方面以及何种标准（国际 标准、国家标准、地方标准、行业标准），开展的临床研究涉及哪个方面以及处于何种阶段（I 期、II 期、III 期）等。
5.“是否里程碑成果”，作为项目“里程碑”节点检查考核的依据，控制在 3 个以内。
6.“考核方式方法”，应提出符合相关研究成果与指标的具体考核技术方法、测算方法等。

四、计划进度目标                                                       
起止年月	进度目标要求

2025-01-01	
至	
2025-03-31	　方案整体设计、技术调研，完成代码特征快 速提取、软件包成分精准识别等关键技术研 究及突破，完成面向源代码与二进制代码的 软件成分分析技术研究。

2025-04-01	
至	
2025-06-30	　完成软件风险量化评估等关键技术研究与突 破，研发针对异构复杂软件供应链的风险评 估体系。完成平台调研、系统设计报告和技 术。
2025-07-01	至	2025-09-30	　完成海量开源项目监控与源数据采集系统、 分布式海量代码特征基因提取系统的原型研 制，实现第一个里程碑。
2025-10-01	至	2025-12-31	　完成异构复杂软件供应链分析及风险评估平 台原型研发，实现第二个里程碑；完成 5 个 技术发明专利申请受理，5 个软件著作权。

2026-01-01	
至	
2026-03-31	　完成在运营商行业开展基于信创环境下的异 构复杂软件供应链安全治理示范应用，并基 于运营商场景完成技术验证、改进和应用。

2026-04-01	
至	
2026-06-30	基于运营商场景完成技术验证和改进优化， 并完成对国产化软硬件场景的适配兼容，形 成其他行业集成试点方案模板。


2026-07-01	

至	

2026-12-31	　完 5 个技术发明专利申请受理，6 个软件著作， 2 个论文期刊；
　完成在国防军工、金融、能源、政务、企业 等行业示范应用，并根据反馈进行平台和技 术优化，并完成项目验收。
　注：一般按每 3 个月制定项目计划进度，将项目主要研发内容及考核指标分解落 实到各阶段。

